#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le module de suivi des changements (filesystem).
"""

import unittest
import tempfile
import shutil
import os
import json
from datetime import datetime
from unittest.mock import Mock, patch
import pytest

from confluence.tracking import ConfluenceChangeTracker
from confluence.models import ContentItem, AttachmentDetail, SpaceInfo, UserInfo


@pytest.mark.unit
@pytest.mark.tracking
@pytest.mark.filesystem
class TestConfluenceChangeTracker(unittest.TestCase):
    """Tests pour la classe ConfluenceChangeTracker."""

    def setUp(self):
        """Configuration des tests."""
        # Créer un répertoire temporaire pour les tests
        self.temp_dir = tempfile.mkdtemp()
        self.tracker = ConfluenceChangeTracker(storage_dir=self.temp_dir)

        # Créer des objets de test
        self.space_info = SpaceInfo(
            id="1",
            key="TEST",
            name="Test Space",
            type="global"
        )

        self.user_info = UserInfo(
            id="user1",
            username="testuser",
            display_name="Test User"
        )

        self.content_item = Mock()
        self.content_item.id = "123"
        self.content_item.title = "Test Page"
        self.content_item.version = {"number": 1}
        self.content_item.last_updated = "2023-01-01T12:00:00"  # String au lieu de datetime
        self.content_item.body_storage = "<p>Test content</p>"
        self.content_item.space = self.space_info
        self.content_item.attachments = []

        self.attachment = Mock()
        self.attachment.id = "att123"
        self.attachment.title = "test.pdf"
        self.attachment.file_name = "test.pdf"
        self.attachment.file_size = 1024
        self.attachment.media_type = "application/pdf"
        self.attachment.created = "2023-01-01T12:00:00"  # String au lieu de datetime

    def tearDown(self):
        """Nettoyage après les tests."""
        # Supprimer le répertoire temporaire
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_tracker_initialization(self):
        """Test de l'initialisation du tracker."""
        self.assertEqual(self.tracker.storage_dir, self.temp_dir)
        self.assertIsNotNone(self.tracker.logger)

        # Vérifier que les répertoires sont créés
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "content_hashes")))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "attachment_hashes")))
        self.assertTrue(os.path.exists(os.path.join(self.temp_dir, "sync_history")))

    def test_has_content_changed_new_content(self):
        """Test de détection de changement pour un nouveau contenu."""
        # Premier appel - le contenu n'existe pas encore
        result = self.tracker.has_content_changed(self.content_item)

        self.assertTrue(result)

        # Vérifier que le fichier de hash a été créé
        hash_file = os.path.join(self.temp_dir, "content_hashes", "123.json")
        self.assertTrue(os.path.exists(hash_file))

    def test_has_content_changed_unchanged_content(self):
        """Test de détection de changement pour un contenu inchangé."""
        # Premier appel pour créer le hash
        self.tracker.has_content_changed(self.content_item)

        # Deuxième appel avec le même contenu
        result = self.tracker.has_content_changed(self.content_item)

        self.assertFalse(result)

    def test_has_content_changed_modified_content(self):
        """Test de détection de changement pour un contenu modifié."""
        # Premier appel pour créer le hash
        self.tracker.has_content_changed(self.content_item)

        # Modifier le contenu
        self.content_item.body_storage = "<p>Modified content</p>"

        # Deuxième appel avec le contenu modifié
        result = self.tracker.has_content_changed(self.content_item)

        self.assertTrue(result)

    def test_has_content_changed_corrupted_hash_file(self):
        """Test de gestion d'un fichier de hash corrompu."""
        # Créer un fichier de hash corrompu
        hash_file = os.path.join(self.temp_dir, "content_hashes", "123.json")
        with open(hash_file, 'w') as f:
            f.write("invalid json")

        # Le tracker devrait considérer que le contenu a changé
        result = self.tracker.has_content_changed(self.content_item)

        self.assertTrue(result)

    def test_has_attachment_changed_new_attachment(self):
        """Test de détection de changement pour une nouvelle pièce jointe."""
        # Premier appel - la pièce jointe n'existe pas encore
        result = self.tracker.has_attachment_changed(self.attachment)

        self.assertTrue(result)

        # Vérifier que le fichier de hash a été créé
        hash_file = os.path.join(self.temp_dir, "attachment_hashes", "att123.json")
        self.assertTrue(os.path.exists(hash_file))

    def test_has_attachment_changed_unchanged_attachment(self):
        """Test de détection de changement pour une pièce jointe inchangée."""
        # Premier appel pour créer le hash
        self.tracker.has_attachment_changed(self.attachment)

        # Deuxième appel avec la même pièce jointe
        result = self.tracker.has_attachment_changed(self.attachment)

        self.assertFalse(result)

    def test_has_attachment_changed_modified_attachment(self):
        """Test de détection de changement pour une pièce jointe modifiée."""
        # Premier appel pour créer le hash
        self.tracker.has_attachment_changed(self.attachment)

        # Modifier la pièce jointe
        self.attachment.file_size = 2048

        # Deuxième appel avec la pièce jointe modifiée
        result = self.tracker.has_attachment_changed(self.attachment)

        self.assertTrue(result)

    @patch('confluence_rag.tracking.CorrelationContext.get_correlation_id')
    def test_record_sync(self, mock_correlation):
        """Test d'enregistrement d'une synchronisation."""
        mock_correlation.return_value = "test-correlation-123"

        # Créer un contenu simple sans objets Mock complexes
        simple_content = Mock()
        simple_content.id = "123"
        simple_content.title = "Test Page"
        simple_content.type = "page"
        simple_content.space = Mock()
        simple_content.space.key = "TEST"
        simple_content.attachments = []

        content_items = [simple_content]

        result = self.tracker.record_sync(content_items)

        # Vérifier les statistiques retournées
        self.assertIn("sync_id", result)
        self.assertIn("timestamp", result)
        self.assertEqual(result["correlation_id"], "test-correlation-123")
        self.assertEqual(result["total_content_items"], 1)
        self.assertEqual(result["total_attachments"], 0)
        self.assertEqual(result["spaces_processed"], ["TEST"])
        self.assertEqual(result["content_ids"], ["123"])

        # Vérifier que le fichier de synchronisation a été créé
        sync_files = os.listdir(os.path.join(self.temp_dir, "sync_history"))
        self.assertEqual(len(sync_files), 1)

    def test_get_last_sync_info_no_history(self):
        """Test de récupération des informations de dernière sync sans historique."""
        result = self.tracker.get_last_sync_info()

        self.assertIsNone(result)

    def test_get_last_sync_info_with_history(self):
        """Test de récupération des informations de dernière sync avec historique."""
        # Créer un contenu simple sans objets Mock complexes
        simple_content = Mock()
        simple_content.id = "123"
        simple_content.title = "Test Page"
        simple_content.type = "page"
        simple_content.space = Mock()
        simple_content.space.key = "TEST"
        simple_content.attachments = []

        # Enregistrer une synchronisation
        content_items = [simple_content]
        sync_stats = self.tracker.record_sync(content_items)

        # Récupérer les informations de dernière sync
        result = self.tracker.get_last_sync_info()

        self.assertIsNotNone(result)
        self.assertEqual(result["sync_id"], sync_stats["sync_id"])
        self.assertEqual(result["total_content_items"], 1)

    def test_generate_content_hash(self):
        """Test de génération de hash pour un contenu."""
        hash1 = self.tracker._generate_content_hash(self.content_item)
        hash2 = self.tracker._generate_content_hash(self.content_item)

        # Le hash doit être identique pour le même contenu
        self.assertEqual(hash1, hash2)
        self.assertIsInstance(hash1, str)
        self.assertEqual(len(hash1), 64)  # SHA256 hash length

    def test_generate_attachment_hash(self):
        """Test de génération de hash pour une pièce jointe."""
        hash1 = self.tracker._generate_attachment_hash(self.attachment)
        hash2 = self.tracker._generate_attachment_hash(self.attachment)

        # Le hash doit être identique pour la même pièce jointe
        self.assertEqual(hash1, hash2)
        self.assertIsInstance(hash1, str)
        self.assertEqual(len(hash1), 64)  # SHA256 hash length

    def test_save_content_hash(self):
        """Test de sauvegarde de hash de contenu."""
        content_id = "test123"
        content_hash = "abcd1234"

        self.tracker._save_content_hash(content_id, content_hash)

        # Vérifier que le fichier a été créé
        hash_file = os.path.join(self.temp_dir, "content_hashes", f"{content_id}.json")
        self.assertTrue(os.path.exists(hash_file))

        # Vérifier le contenu du fichier
        with open(hash_file, 'r') as f:
            data = json.load(f)
            self.assertEqual(data["id"], content_id)
            self.assertEqual(data["hash"], content_hash)
            self.assertIn("timestamp", data)

    def test_save_attachment_hash(self):
        """Test de sauvegarde de hash de pièce jointe."""
        attachment_id = "att123"
        attachment_hash = "efgh5678"

        self.tracker._save_attachment_hash(attachment_id, attachment_hash)

        # Vérifier que le fichier a été créé
        hash_file = os.path.join(self.temp_dir, "attachment_hashes", f"{attachment_id}.json")
        self.assertTrue(os.path.exists(hash_file))

        # Vérifier le contenu du fichier
        with open(hash_file, 'r') as f:
            data = json.load(f)
            self.assertEqual(data["id"], attachment_id)
            self.assertEqual(data["hash"], attachment_hash)
            self.assertIn("timestamp", data)

    def test_count_content_types(self):
        """Test de comptage des types de contenu."""
        # Créer plusieurs contenus de types différents
        content1 = Mock()
        content1.type = "page"
        content2 = Mock()
        content2.type = "page"
        content3 = Mock()
        content3.type = "blogpost"

        content_items = [content1, content2, content3]

        result = self.tracker._count_content_types(content_items)

        self.assertEqual(result["page"], 2)
        self.assertEqual(result["blogpost"], 1)

    @patch('confluence_rag.tracking.CorrelationContext.get_correlation_id')
    def test_record_sync_no_correlation_id(self, mock_correlation):
        """Test d'enregistrement de sync sans correlation ID."""
        mock_correlation.return_value = None

        content_items = [self.content_item]

        result = self.tracker.record_sync(content_items)

        self.assertEqual(result["correlation_id"], "no-correlation-id")

    def test_record_sync_with_attachments(self):
        """Test d'enregistrement de sync avec pièces jointes."""
        # Créer un contenu simple avec pièces jointes
        simple_content = Mock()
        simple_content.id = "123"
        simple_content.title = "Test Page"
        simple_content.type = "page"
        simple_content.space = Mock()
        simple_content.space.key = "TEST"

        # Créer des pièces jointes simples
        simple_attachment1 = Mock()
        simple_attachment1.id = "att1"
        simple_attachment2 = Mock()
        simple_attachment2.id = "att2"
        simple_content.attachments = [simple_attachment1, simple_attachment2]

        content_items = [simple_content]

        result = self.tracker.record_sync(content_items)

        self.assertEqual(result["total_attachments"], 2)

    def test_hash_consistency_across_restarts(self):
        """Test de cohérence des hash entre redémarrages."""
        # Premier tracker
        hash1 = self.tracker._generate_content_hash(self.content_item)

        # Nouveau tracker avec le même répertoire
        tracker2 = ConfluenceChangeTracker(storage_dir=self.temp_dir)
        hash2 = tracker2._generate_content_hash(self.content_item)

        # Les hash doivent être identiques
        self.assertEqual(hash1, hash2)


@pytest.mark.unit
@pytest.mark.tracking
@pytest.mark.filesystem
class TestTrackingIntegration(unittest.TestCase):
    """Tests d'intégration pour le système de tracking."""

    def setUp(self):
        """Configuration des tests d'intégration."""
        self.temp_dir = tempfile.mkdtemp()
        self.tracker = ConfluenceChangeTracker(storage_dir=self.temp_dir)

    def tearDown(self):
        """Nettoyage après les tests."""
        shutil.rmtree(self.temp_dir)

    def test_tracking_workflow_complete(self):
        """Test du workflow complet de tracking."""
        # Créer un contenu de test
        space_info = SpaceInfo(id="123", key="TEST", name="Test Space", type="global")
        user_info = UserInfo(id="user123", username="testuser", display_name="Test User", email="<EMAIL>")

        content_item = ContentItem(
            id="workflow_test",
            type="page",
            status="current",
            title="Workflow Test Page",
            space=space_info,
            version={"number": 1},
            created=datetime.now(),
            creator=user_info,
            last_updated=datetime.now(),
            last_updater=user_info,
            content_url="https://example.com/content/workflow_test",
            web_ui_url="https://example.com/pages/workflow_test",
            body_storage="<p>Initial content</p>",
            body_plain="Initial content",
            attachments=[]
        )

        # 1. Premier passage - nouveau contenu
        changed = self.tracker.has_content_changed(content_item)
        self.assertTrue(changed, "Le nouveau contenu doit être détecté comme changé")

        # 2. Deuxième passage - contenu inchangé
        changed = self.tracker.has_content_changed(content_item)
        self.assertFalse(changed, "Le contenu inchangé ne doit pas être détecté comme changé")

        # 3. Modification du contenu
        content_item.body_storage = "<p>Modified content</p>"
        content_item.version = {"number": 2}

        changed = self.tracker.has_content_changed(content_item)
        self.assertTrue(changed, "Le contenu modifié doit être détecté comme changé")

        # 4. Enregistrement de synchronisation
        sync_info = self.tracker.record_sync([content_item])
        self.assertIsNotNone(sync_info)
        self.assertIn("sync_id", sync_info)


if __name__ == '__main__':
    unittest.main()

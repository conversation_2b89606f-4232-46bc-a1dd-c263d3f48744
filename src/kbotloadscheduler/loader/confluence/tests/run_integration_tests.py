#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script pour exécuter les tests d'intégration avec l'espace de test complet.
"""

import os
import sys
import argparse
import subprocess
import tempfile
import json
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from confluence.tests.fixtures import TestDataFactory, create_test_page_hierarchy
from confluence.config import SearchCriteria, ConfluenceConfig, StorageConfig, ProcessingConfig


def setup_test_environment():
    """Configure l'environnement de test."""
    print("🔧 Configuration de l'environnement de test...")
    
    # Créer le répertoire temporaire
    temp_dir = tempfile.mkdtemp(prefix="confluence_rag_integration_")
    print(f"📁 Répertoire temporaire : {temp_dir}")
    
    # Générer les fichiers d'exemple
    try:
        from confluence.tests.fixtures.generate_sample_files import generate_files
        generate_files()
        print("✅ Fichiers d'exemple générés")
    except Exception as e:
        print(f"⚠️  Avertissement : {e}")
    
    return temp_dir


def create_test_configuration(temp_dir: str):
    """Crée les configurations de test."""
    print("⚙️  Création des configurations de test...")
    
    # Configuration Confluence
    config = ConfluenceConfig(
        url="https://test.atlassian.net",
        username="<EMAIL>", 
        api_token="test_token_for_integration_tests"
    )
    
    # Critères de recherche
    criteria = SearchCriteria(
        spaces=["TESTSPACE"],
        types=["page", "blogpost"],
        max_results=100,
        include_attachments=True,
        include_children=True
    )
    
    # Configuration de stockage
    storage_config = StorageConfig(
        storage_type="filesystem",
        output_dir=temp_dir,
        attachment_extensions_to_download_raw=['.pdf', '.docx', '.xlsx', '.png', '.drawio'],
        attachment_extensions_to_convert=['.txt', '.json', '.csv', '.html']
    )
    
    # Configuration de traitement
    processing_config = ProcessingConfig(
        chunk_size=500,
        overlap_size=50,
        max_parallel_downloads=3,
        max_thread_workers=2
    )
    
    return config, criteria, storage_config, processing_config


def display_test_data_summary():
    """Affiche un résumé des données de test."""
    print("\n📊 Résumé des données de test :")
    print("=" * 50)
    
    factory = TestDataFactory()
    pages = factory.create_page_hierarchy()
    
    print(f"🏢 Espace : {factory.space.name} ({factory.space.key})")
    print(f"📄 Nombre de pages : {len(pages)}")
    
    # Analyser la hiérarchie
    root_pages = [p for p in pages if p.parent_id is None]
    child_pages = [p for p in pages if p.parent_id is not None]
    
    print(f"📋 Pages racines : {len(root_pages)}")
    print(f"📑 Sous-pages : {len(child_pages)}")
    
    # Types de contenu
    page_types = {}
    for page in pages:
        page_types[page.type] = page_types.get(page.type, 0) + 1
    
    print(f"📝 Types de contenu :")
    for content_type, count in page_types.items():
        print(f"   - {content_type}: {count}")
    
    # Pièces jointes
    total_attachments = 0
    for page in pages:
        if hasattr(page, 'attachments') and page.attachments:
            total_attachments += len(page.attachments)
    
    print(f"📎 Pièces jointes simulées : {total_attachments}")
    
    print("\n📋 Structure hiérarchique :")
    for page in root_pages:
        print(f"   📄 {page.title} ({page.id})")
        children = [p for p in pages if p.parent_id == page.id]
        for child in children:
            print(f"      └── 📑 {child.title} ({child.id})")
            grandchildren = [p for p in pages if p.parent_id == child.id]
            for grandchild in grandchildren:
                print(f"          └── 📄 {grandchild.title} ({grandchild.id})")


def run_integration_tests(test_pattern: str = None, verbose: bool = False):
    """Exécute les tests d'intégration."""
    print("\n🧪 Exécution des tests d'intégration...")
    
    # Construire la commande pytest
    cmd = ["python", "-m", "pytest"]
    
    # Ajouter le fichier de test d'intégration
    test_file = "confluence_rag/tests/test_integration_complete.py"
    cmd.append(test_file)
    
    # Options pytest
    cmd.extend(["-v", "-s"])  # Verbose et pas de capture de sortie
    cmd.extend(["-m", "integration"])  # Seulement les tests d'intégration
    
    if test_pattern:
        cmd.extend(["-k", test_pattern])
    
    if verbose:
        cmd.append("--tb=long")
    
    # Variables d'environnement pour les tests
    env = os.environ.copy()
    env["CONFLUENCE_RAG_ENVIRONMENT"] = "test"
    env["LOG_LEVEL"] = "INFO"
    
    print(f"🚀 Commande : {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, env=env, check=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution des tests : {e}")
        return False


def run_specific_test_scenario():
    """Exécute un scénario de test spécifique avec affichage détaillé."""
    print("\n🎯 Exécution d'un scénario de test spécifique...")
    
    temp_dir = setup_test_environment()
    config, criteria, storage_config, processing_config = create_test_configuration(temp_dir)
    
    try:
        # Importer et exécuter directement un test
        from confluence.tests.test_integration_complete import TestCompleteIntegration
        
        test_instance = TestCompleteIntegration()
        test_instance.setUp()
        
        print("✅ Test configuré avec succès")
        print(f"📁 Données de test dans : {test_instance.temp_dir}")
        print(f"📊 Nombre de pages de test : {len(test_instance.test_pages)}")
        
        # Afficher quelques détails sur les données
        for i, page in enumerate(test_instance.test_pages[:3]):
            print(f"   📄 Page {i+1}: {page.title} ({page.type})")
            if page.attachments:
                print(f"      📎 {len(page.attachments)} pièce(s) jointe(s)")
        
        test_instance.tearDown()
        print("🧹 Nettoyage terminé")
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Nettoyer le répertoire temporaire
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


def main():
    """Fonction principale."""
    parser = argparse.ArgumentParser(description="Tests d'intégration avec espace de test complet")
    parser.add_argument("--summary", action="store_true", help="Affiche un résumé des données de test")
    parser.add_argument("--run-tests", action="store_true", help="Exécute les tests d'intégration")
    parser.add_argument("--scenario", action="store_true", help="Exécute un scénario de test spécifique")
    parser.add_argument("--pattern", "-k", help="Pattern pour filtrer les tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Mode verbeux")
    
    args = parser.parse_args()
    
    print("🚀 Tests d'Intégration - Espace de Test Confluence")
    print("=" * 60)
    
    if args.summary:
        display_test_data_summary()
    
    if args.scenario:
        run_specific_test_scenario()
    
    if args.run_tests:
        success = run_integration_tests(args.pattern, args.verbose)
        if success:
            print("\n✅ Tous les tests d'intégration ont réussi !")
        else:
            print("\n❌ Certains tests d'intégration ont échoué.")
            sys.exit(1)
    
    if not any([args.summary, args.run_tests, args.scenario]):
        print("\n💡 Utilisation :")
        print("   --summary     : Affiche les données de test")
        print("   --run-tests   : Exécute les tests d'intégration")
        print("   --scenario    : Exécute un scénario spécifique")
        print("   --pattern     : Filtre les tests")
        print("   --verbose     : Mode verbeux")
        print("\nExemple :")
        print("   python run_integration_tests.py --summary")
        print("   python run_integration_tests.py --run-tests --verbose")


if __name__ == "__main__":
    main()

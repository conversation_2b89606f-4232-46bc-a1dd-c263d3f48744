#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le processeur Draw.io.
"""

import unittest
import logging
import base64
import zlib
import xml.etree.ElementTree as ET
from unittest.mock import Mock, patch

from confluence.processing.drawio_processor import DrawIOProcessor
from confluence.processing.enums import ExtractionResult, DrawIOMetadata, MediaType


class TestDrawIOProcessor(unittest.TestCase):
    """Tests pour la classe DrawIOProcessor."""

    def setUp(self):
        """Configuration des tests."""
        self.logger = logging.getLogger(__name__)
        self.processor = DrawIOProcessor(self.logger)

    def test_drawio_processor_initialization(self):
        """Test de l'initialisation du processeur Draw.io."""
        self.assertIsNotNone(self.processor.logger)
        self.assertEqual(self.processor.logger, self.logger)

    def test_can_process_drawio_media_type(self):
        """Test de reconnaissance du type MIME Draw.io."""
        self.assertTrue(self.processor.can_process("diagram.drawio", MediaType.DRAWIO.value))
        self.assertFalse(self.processor.can_process("document.pdf", "application/pdf"))

    def test_can_process_drawio_extension(self):
        """Test de reconnaissance de l'extension Draw.io."""
        self.assertTrue(self.processor.can_process("diagram.drawio", "unknown/type"))
        self.assertTrue(self.processor.can_process("DIAGRAM.DRAWIO", "unknown/type"))  # Case insensitive
        self.assertFalse(self.processor.can_process("document.pdf", "unknown/type"))

    def test_is_drawio_xml_valid_content(self):
        """Test de détection de contenu XML Draw.io valide."""
        valid_content = b'<mxfile><diagram><mxGraphModel>content</mxGraphModel></diagram></mxfile>'
        self.assertTrue(self.processor.is_drawio_xml(valid_content))

    def test_is_drawio_xml_with_mxgraph(self):
        """Test de détection avec contenu mxgraph."""
        valid_content = b'<mxfile><mxgraph><diagram>content</diagram></mxgraph></mxfile>'
        self.assertTrue(self.processor.is_drawio_xml(valid_content))

    def test_is_drawio_xml_invalid_content(self):
        """Test de détection avec contenu XML non Draw.io."""
        invalid_content = b'<root><document>not a diagram</document></root>'
        self.assertFalse(self.processor.is_drawio_xml(invalid_content))

    def test_is_drawio_xml_non_xml_content(self):
        """Test de détection avec contenu non XML."""
        non_xml_content = b'This is not XML content'
        self.assertFalse(self.processor.is_drawio_xml(non_xml_content))

    def test_is_drawio_xml_encoding_error(self):
        """Test de détection avec erreur d'encodage."""
        # Contenu binaire invalide
        invalid_bytes = b'\xff\xfe\x00\x00invalid'
        self.assertFalse(self.processor.is_drawio_xml(invalid_bytes))

    def test_extract_text_simple_diagram(self):
        """Test d'extraction de texte d'un diagramme simple."""
        simple_diagram = '''<?xml version="1.0" encoding="UTF-8"?>
        <mxfile version="1.0">
            <diagram name="Test Diagram">
                <mxGraphModel>
                    <root>
                        <mxCell id="0"/>
                        <mxCell id="1" parent="0"/>
                        <mxCell id="2" value="Test Shape" vertex="1" parent="1"/>
                    </root>
                </mxGraphModel>
            </diagram>
        </mxfile>'''

        content_bytes = simple_diagram.encode('utf-8')
        result = self.processor.extract_text(content_bytes)

        self.assertTrue(result.success)
        self.assertIn("Test Diagram", result.text)
        self.assertEqual(result.metadata["type"], "drawio_diagram")

    def test_extract_text_with_metadata(self):
        """Test d'extraction avec métadonnées complètes."""
        diagram_with_metadata = '''<?xml version="1.0" encoding="UTF-8"?>
        <mxfile version="2.0" userAgent="TestAgent" created="2023-01-01" modified="2023-01-02">
            <diagram name="Main Page">
                <mxGraphModel>
                    <root>
                        <mxCell id="0"/>
                        <mxCell id="1" parent="0"/>
                        <mxCell id="2" value="Shape 1" vertex="1" parent="1"/>
                        <mxCell id="3" value="Shape 2" vertex="1" parent="1"/>
                        <mxCell id="4" value="" edge="1" parent="1" source="2" target="3"/>
                    </root>
                </mxGraphModel>
            </diagram>
        </mxfile>'''

        content_bytes = diagram_with_metadata.encode('utf-8')
        result = self.processor.extract_text(content_bytes)

        self.assertTrue(result.success)
        self.assertIn("Main Page", result.text)
        self.assertIn("Author: TestAgent", result.text)
        self.assertIn("Created: 2023-01-01", result.text)
        self.assertIn("Modified: 2023-01-02", result.text)
        # Les shapes ne sont pas comptés car les données ne sont pas dans le format encodé attendu

    def test_extract_text_multiple_pages(self):
        """Test d'extraction avec plusieurs pages."""
        multi_page_diagram = '''<?xml version="1.0" encoding="UTF-8"?>
        <mxfile>
            <diagram name="Page 1">
                <mxGraphModel>
                    <root>
                        <mxCell id="0"/>
                        <mxCell id="1" parent="0"/>
                        <mxCell id="2" value="Page 1 Content" vertex="1" parent="1"/>
                    </root>
                </mxGraphModel>
            </diagram>
            <diagram name="Page 2">
                <mxGraphModel>
                    <root>
                        <mxCell id="0"/>
                        <mxCell id="1" parent="0"/>
                        <mxCell id="2" value="Page 2 Content" vertex="1" parent="1"/>
                    </root>
                </mxGraphModel>
            </diagram>
        </mxfile>'''

        content_bytes = multi_page_diagram.encode('utf-8')
        result = self.processor.extract_text(content_bytes)

        self.assertTrue(result.success)
        self.assertIn("Pages (2): Page 1, Page 2", result.text)
        # Le contenu des pages n'est pas extrait car les données ne sont pas encodées
        # dans un format que le processeur peut décoder

    def test_extract_text_invalid_xml(self):
        """Test d'extraction avec XML invalide."""
        invalid_xml = b'<invalid><unclosed>tag</invalid>'

        result = self.processor.extract_text(invalid_xml)

        self.assertFalse(result.success)
        self.assertIn("Draw.io extraction error", result.error_message)

    def test_extract_text_empty_content(self):
        """Test d'extraction avec contenu vide."""
        result = self.processor.extract_text(b'')

        self.assertFalse(result.success)
        self.assertIn("Draw.io extraction error", result.error_message)

    def test_extract_drawio_metadata_basic(self):
        """Test d'extraction de métadonnées basiques."""
        xml_content = '''<mxfile version="1.0" userAgent="TestAgent">
            <diagram name="Test Page">
                <mxGraphModel>
                    <root>
                        <mxCell id="0"/>
                        <mxCell id="1" parent="0"/>
                        <mxCell id="2" value="Test Shape" vertex="1" parent="1"/>
                    </root>
                </mxGraphModel>
            </diagram>
        </mxfile>'''

        root = ET.fromstring(xml_content)
        metadata = self.processor._extract_drawio_metadata(root)

        self.assertEqual(metadata.author, "TestAgent")
        self.assertEqual(metadata.version, "1.0")
        self.assertEqual(metadata.page_count, 1)
        self.assertEqual(metadata.page_names, ["Test Page"])
        self.assertEqual(metadata.title, "Test Page")
        # Les shapes ne sont comptés que si les données mxGraphModel sont décodées
        # Dans ce test, les données ne sont pas encodées donc pas de shapes comptés
        self.assertEqual(metadata.shape_count, 0)
        self.assertEqual(metadata.connector_count, 0)

    def test_extract_drawio_metadata_no_diagrams(self):
        """Test d'extraction de métadonnées sans diagrammes."""
        xml_content = '<mxfile version="1.0"></mxfile>'

        root = ET.fromstring(xml_content)
        metadata = self.processor._extract_drawio_metadata(root)

        self.assertEqual(metadata.page_count, 0)
        self.assertEqual(metadata.page_names, [])
        self.assertEqual(metadata.shape_count, 0)
        self.assertEqual(metadata.connector_count, 0)

    def test_decode_mxgraph_data_url_encoded(self):
        """Test de décodage de données URL encodées."""
        # Créer des données XML simples et les encoder
        xml_data = '<mxGraphModel><root><mxCell id="0"/></root></mxGraphModel>'
        encoded_data = xml_data.replace('<', '%3C').replace('>', '%3E')

        result = self.processor._decode_mxgraph_data(encoded_data)

        self.assertIsNotNone(result)
        self.assertEqual(result.tag, 'mxGraphModel')

    def test_decode_mxgraph_data_base64_compressed(self):
        """Test de décodage de données base64 compressées."""
        # Créer des données XML, les compresser et les encoder en base64
        xml_data = '<mxGraphModel><root><mxCell id="0"/></root></mxGraphModel>'
        compressed = zlib.compress(xml_data.encode('utf-8'))
        encoded_data = base64.b64encode(compressed).decode('ascii')

        result = self.processor._decode_mxgraph_data(encoded_data)

        self.assertIsNotNone(result)
        self.assertEqual(result.tag, 'mxGraphModel')

    def test_decode_mxgraph_data_plain_xml(self):
        """Test de décodage de données XML simples."""
        xml_data = '<mxGraphModel><root><mxCell id="0"/></root></mxGraphModel>'

        result = self.processor._decode_mxgraph_data(xml_data)

        self.assertIsNotNone(result)
        self.assertEqual(result.tag, 'mxGraphModel')

    def test_decode_mxgraph_data_invalid(self):
        """Test de décodage de données invalides."""
        invalid_data = "not valid xml or encoded data"

        result = self.processor._decode_mxgraph_data(invalid_data)

        self.assertIsNone(result)

    def test_is_base64_valid(self):
        """Test de détection de chaîne base64 valide."""
        valid_base64 = base64.b64encode(b"test data").decode('ascii')
        self.assertTrue(self.processor._is_base64(valid_base64))

    def test_is_base64_invalid(self):
        """Test de détection de chaîne base64 invalide."""
        invalid_base64 = "not base64 data!"
        self.assertFalse(self.processor._is_base64(invalid_base64))

    def test_clean_diagram_text_html_entities(self):
        """Test de nettoyage de texte avec entités HTML."""
        html_text = "&lt;div&gt;Test &amp; Content&lt;/div&gt;"
        cleaned = self.processor._clean_diagram_text(html_text)

        self.assertIn("Test & Content", cleaned)
        self.assertNotIn("&lt;", cleaned)
        self.assertNotIn("&gt;", cleaned)

    def test_clean_diagram_text_html_tags(self):
        """Test de nettoyage de texte avec balises HTML."""
        html_text = "<div><b>Bold Text</b><br/>Line break</div>"
        cleaned = self.processor._clean_diagram_text(html_text)

        self.assertIn("Bold Text", cleaned)
        self.assertNotIn("<div>", cleaned)
        self.assertNotIn("<b>", cleaned)

    def test_format_drawio_content_complete(self):
        """Test de formatage de contenu Draw.io complet."""
        metadata = DrawIOMetadata(
            title="Test Diagram",
            description="A test diagram",
            author="Test Author",
            created="2023-01-01",
            modified="2023-01-02",
            page_count=2,
            page_names=["Page 1", "Page 2"],
            shape_count=5,
            connector_count=3,
            text_elements=["Shape 1", "Shape 2", "Connection Label"],
            layers=["Layer 1", "Background"]
        )

        formatted = self.processor._format_drawio_content(metadata)

        self.assertIn("Diagram Title: Test Diagram", formatted)
        self.assertIn("Description: A test diagram", formatted)
        self.assertIn("Author: Test Author", formatted)
        self.assertIn("Pages (2): Page 1, Page 2", formatted)
        self.assertIn("5 shapes, 3 connectors", formatted)
        self.assertIn("Layers: Layer 1, Background", formatted)
        self.assertIn("Text Content:", formatted)
        self.assertIn("Shape 1", formatted)
        self.assertIn("Created: 2023-01-01", formatted)
        self.assertIn("Modified: 2023-01-02", formatted)

    def test_format_drawio_content_minimal(self):
        """Test de formatage de contenu Draw.io minimal."""
        metadata = DrawIOMetadata()

        formatted = self.processor._format_drawio_content(metadata)

        # Le contenu formaté ne devrait pas être vide même avec des métadonnées vides
        self.assertIsInstance(formatted, str)

    def test_extract_diagram_elements_shapes_and_connectors(self):
        """Test d'extraction d'éléments de diagramme."""
        xml_content = '''<mxGraphModel>
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="Shape 1" vertex="1" parent="1"/>
                <mxCell id="3" value="Shape 2" vertex="1" parent="1"/>
                <mxCell id="4" value="Connection" edge="1" parent="1" source="2" target="3"/>
            </root>
        </mxGraphModel>'''

        root = ET.fromstring(xml_content)
        metadata = DrawIOMetadata()

        self.processor._extract_diagram_elements(root, metadata)

        self.assertEqual(metadata.shape_count, 2)  # 2 vertex cells
        self.assertEqual(metadata.connector_count, 1)  # 1 edge cell
        self.assertIn("Shape 1", metadata.text_elements)
        self.assertIn("Shape 2", metadata.text_elements)
        self.assertIn("Connection", metadata.text_elements)


if __name__ == '__main__':
    unittest.main()

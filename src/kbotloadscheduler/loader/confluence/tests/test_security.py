#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le module de sécurité.
"""

import unittest

from confluence.security import SecurityUtils


class TestSecurityUtils(unittest.TestCase):
    """Tests pour la classe SecurityUtils."""

    def test_sanitize_error_message_pat_tokens(self):
        """Test du masquage des Personal Access Tokens."""
        test_cases = [
            ("Error with token ABC123XYZ456789012345", "Error with token ***TOKEN***"),
            ("Authentication failed: ABCDEFGHIJKLMNOPQRSTUVWXYZ123456", "Authentication failed: ***TOKEN***"),
            ("Multiple tokens: ABC123 and XYZ789012345678901234", "Multiple tokens: ***TOKEN*** and ***TOKEN***"),
            ("Short token AB12 should not be masked", "Short token AB12 should not be masked"),  # Trop court
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = SecurityUtils.sanitize_error_message(original)
                self.assertEqual(result, expected)

    def test_sanitize_error_message_email_tokens(self):
        """Test du masquage des tokens avec email."""
        test_cases = [
            ("Auth: <EMAIL>:ABCD1234567890123456", "Auth: ***EMAIL:TOKEN***"),
            ("<NAME_EMAIL>:XYZ9876543210987654", "Failed login ***EMAIL:TOKEN***"),
            ("Config: <EMAIL>:TOKEN123456789012345", "Config: ***EMAIL:TOKEN***"),
            ("Invalid: <EMAIL>:SHORT", "Invalid: <EMAIL>:SHORT"),  # Token trop court
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = SecurityUtils.sanitize_error_message(original)
                self.assertEqual(result, expected)

    def test_sanitize_error_message_authorization_headers(self):
        """Test du masquage des headers Authorization."""
        test_cases = [
            ("Authorization: Bearer ABC123XYZ456", "Authorization: Bearer ***TOKEN***"),
            ("Authorization: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==", "Authorization: Basic ***TOKEN***"),
            ("Request failed: Authorization: Bearer TOKEN123", "Request failed: Authorization: Bearer ***TOKEN***"),
            ("authorization: bearer lowercase_token", "authorization: bearer ***TOKEN***"),  # Case insensitive
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = SecurityUtils.sanitize_error_message(original)
                self.assertEqual(result, expected)

    def test_sanitize_error_message_url_credentials(self):
        """Test du masquage des identifiants dans les URLs."""
        test_cases = [
            ("https://user:<EMAIL>/api", "https://***USER***:***PASS***@example.com/api"),
            ("Connection to ftp://admin:<EMAIL> failed", "Connection to ftp://***USER***:***PASS***@server.com failed"),
            ("*******************************/path", "http://***USER***:***PASS***@localhost:8080/path"),
            ("https://example.com/api", "https://example.com/api"),  # Pas de credentials
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = SecurityUtils.sanitize_error_message(original)
                self.assertEqual(result, expected)

    def test_sanitize_error_message_url_parameters(self):
        """Test du masquage des paramètres sensibles dans les URLs."""
        test_cases = [
            ("GET /api?token=ABC123&user=john", "GET /api?token=***&user=john"),
            ("Request: /auth?password=secret&key=XYZ789", "Request: /auth?password=***&key=***"),
            ("URL: /api?secret=hidden&public=visible", "URL: /api?secret=***&public=visible"),
            ("Path: /api?apikey=KEY123&data=value", "Path: /api?apikey=***&data=value"),
            ("Query: /search?auth=TOKEN&query=test", "Query: /search?auth=***&query=test"),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = SecurityUtils.sanitize_error_message(original)
                self.assertEqual(result, expected)

    def test_sanitize_error_message_json_keys(self):
        """Test du masquage des clés sensibles dans le JSON."""
        test_cases = [
            ('{"api_token": "ABC123", "user": "john"}', '{"api_token": "***", "user": "john"}'),
            ('Config: "pat_token": "XYZ789"', 'Config: "pat_token": "***"'),
            ('{"password": "secret", "public": "data"}', '{"password": "***", "public": "data"}'),
            ('{"token": "value", "key": "secret"}', '{"token": "***", "key": "***"}'),
            ('{"secret": "hidden", "apikey": "key123"}', '{"secret": "***", "apikey": "***"}'),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = SecurityUtils.sanitize_error_message(original)
                self.assertEqual(result, expected)

    def test_sanitize_error_message_case_insensitive(self):
        """Test que le masquage est insensible à la casse."""
        test_cases = [
            ("AUTHORIZATION: BEARER TOKEN123", "AUTHORIZATION: BEARER ***TOKEN***"),
            ("authorization: basic token456", "authorization: basic ***TOKEN***"),
            ("GET /api?TOKEN=ABC123&user=john", "GET /api?TOKEN=***&user=john"),
            ("GET /api?Password=secret&data=value", "GET /api?Password=***&data=value"),
        ]

        for original, expected in test_cases:
            with self.subTest(original=original):
                result = SecurityUtils.sanitize_error_message(original)
                self.assertEqual(result, expected)

    def test_sanitize_error_message_complex_scenarios(self):
        """Test avec des scénarios complexes contenant plusieurs types de données sensibles."""
        original = (
            "Request failed: POST https://user:<EMAIL>/auth "
            "with headers {'Authorization': 'Bearer ABC123XYZ456789012345'} "
            "and payload {\"api_token\": \"SECRET789\", \"user\": \"john\"}"
        )
        
        result = SecurityUtils.sanitize_error_message(original)
        
        # Vérifier que toutes les données sensibles ont été masquées
        self.assertNotIn(":pass@", result)
        self.assertNotIn("ABC123XYZ456789012345", result)
        self.assertNotIn("\"SECRET789\"", result)
        
        # Vérifier que les données non sensibles sont préservées
        self.assertIn("Request failed", result)
        self.assertIn("POST", result)
        self.assertIn("john", result)
        
        # Vérifier les remplacements
        self.assertIn("***USER***:***PASS***@", result)
        self.assertIn("Bearer ***TOKEN***", result)
        self.assertIn("\"api_token\": \"***\"", result)

    def test_sanitize_error_message_preserve_non_sensitive(self):
        """Test que les données non sensibles sont préservées."""
        test_cases = [
            "Normal log message without sensitive data",
            "User john logged in successfully",
            "Processing 100 items in 5.2 seconds",
            "Configuration loaded from /path/to/config.json",
            "HTTP 200 OK response received",
            "Database connection established",
        ]

        for original in test_cases:
            with self.subTest(original=original):
                result = SecurityUtils.sanitize_error_message(original)
                self.assertEqual(original, result)

    def test_sanitize_error_message_empty_and_none(self):
        """Test avec des messages vides ou None."""
        # Message vide
        result = SecurityUtils.sanitize_error_message("")
        self.assertEqual(result, "")
        
        # Message None (converti en string)
        result = SecurityUtils.sanitize_error_message(None)
        self.assertEqual(result, "None")

    def test_sanitize_error_message_non_string_input(self):
        """Test avec des entrées non-string."""
        # Entier
        result = SecurityUtils.sanitize_error_message(12345)
        self.assertEqual(result, "12345")
        
        # Liste
        result = SecurityUtils.sanitize_error_message(["token", "ABC123XYZ456789012345"])
        self.assertIn("***TOKEN***", result)
        
        # Dictionnaire
        result = SecurityUtils.sanitize_error_message({"token": "ABC123XYZ456789012345"})
        self.assertIn("***TOKEN***", result)

    def test_sensitive_patterns_structure(self):
        """Test de la structure des patterns sensibles."""
        # Vérifier que SENSITIVE_PATTERNS est une liste
        self.assertIsInstance(SecurityUtils.SENSITIVE_PATTERNS, list)
        
        # Vérifier que chaque pattern est un tuple (pattern, replacement)
        for pattern_tuple in SecurityUtils.SENSITIVE_PATTERNS:
            self.assertIsInstance(pattern_tuple, tuple)
            self.assertEqual(len(pattern_tuple), 2)
            pattern, replacement = pattern_tuple
            self.assertIsInstance(pattern, str)
            self.assertIsInstance(replacement, str)

    def test_sanitize_error_message_multiple_occurrences(self):
        """Test avec plusieurs occurrences du même type de données sensibles."""
        original = "Tokens: ABC123XYZ456789012345 and DEF456UVW789012345678 and GHI789RST012345678901"
        result = SecurityUtils.sanitize_error_message(original)
        
        # Vérifier que tous les tokens ont été masqués
        self.assertNotIn("ABC123XYZ456789012345", result)
        self.assertNotIn("DEF456UVW789012345678", result)
        self.assertNotIn("GHI789RST012345678901", result)
        
        # Vérifier que le texte contient les remplacements
        self.assertEqual(result.count("***TOKEN***"), 3)

    def test_sanitize_error_message_edge_cases(self):
        """Test des cas limites."""
        # Token à la limite de longueur (exactement 20 caractères)
        original = "Token: ABCDEFGHIJKLMNOPQRST"
        result = SecurityUtils.sanitize_error_message(original)
        self.assertIn("***TOKEN***", result)
        
        # Token juste en dessous de la limite (19 caractères)
        original = "Token: ABCDEFGHIJKLMNOPQRS"
        result = SecurityUtils.sanitize_error_message(original)
        self.assertNotIn("***TOKEN***", result)  # Ne devrait pas être masqué
        self.assertIn("ABCDEFGHIJKLMNOPQRS", result)


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier la configuration de téléchargement de fichiers bruts.
"""

import os
import sys
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..'))

from confluence.config import StorageConfig

def test_storage_config():
    """Teste les nouvelles options de configuration de stockage."""
    print("Test de StorageConfig avec les nouvelles options de téléchargement brut...")

    # Test de la configuration par défaut
    config = StorageConfig()
    print(f"Configuration par défaut :")
    print(f"  - Extensions à convertir : {config.attachment_extensions_to_convert}")
    print(f"  - Extensions à télécharger en brut : {config.attachment_extensions_to_download_raw}")

    # Test de la configuration par variables d'environnement
    os.environ["ATTACHMENT_EXTENSIONS_TO_CONVERT"] = ".txt,.md,.html"
    os.environ["ATTACHMENT_EXTENSIONS_TO_DOWNLOAD_RAW"] = ".pdf,.xlsx,.docx,.jpg,.png,.gif"

    config_from_env = StorageConfig.from_env()
    print(f"\nConfiguration depuis l'environnement :")
    print(f"  - Extensions à convertir : {config_from_env.attachment_extensions_to_convert}")
    print(f"  - Extensions à télécharger en brut : {config_from_env.attachment_extensions_to_download_raw}")

    # Test de la logique d'extension de fichier
    test_files = [
        "document.pdf",
        "tableur.xlsx",
        "presentation.docx",
        "image.jpg",
        "diagramme.png",
        "readme.txt",
        "notes.md",
        "inconnu.xyz"
    ]

    print(f"\nTest de la logique de traitement des fichiers :")
    for filename in test_files:
        _, ext = os.path.splitext(filename.lower())
        should_convert = ext in config_from_env.attachment_extensions_to_convert
        should_download_raw = ext in config_from_env.attachment_extensions_to_download_raw

        if should_download_raw:
            action = "Téléchargement brut"
        elif should_convert:
            action = "Conversion en texte"
        else:
            action = "Ignoré (non supporté)"

        print(f"  - {filename} ({ext}) : {action}")

if __name__ == "__main__":
    load_dotenv()
    test_storage_config()

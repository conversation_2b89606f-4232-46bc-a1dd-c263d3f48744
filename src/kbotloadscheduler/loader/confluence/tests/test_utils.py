#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le module des utilitaires.
"""

import unittest
import asyncio
from unittest.mock import Mock, patch

from confluence.utils import SecurityValidator, TextProcessor, RetryHandler
from confluence.config import RetryConfig
from confluence.exceptions import (
    SecurityValidationError, APIError, RateLimitExceededError
)


class TestSecurityValidator(unittest.TestCase):
    """Tests pour la classe SecurityValidator."""

    def test_validate_attachment_safe_files(self):
        """Test de validation de fichiers sécurisés."""
        safe_files = [
            ("document.pdf", "application/pdf"),
            ("image.jpg", "image/jpeg"),
            ("spreadsheet.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
            ("text.txt", "text/plain"),
            ("presentation.pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"),
        ]

        for file_name, mime_type in safe_files:
            with self.subTest(file_name=file_name, mime_type=mime_type):
                result = SecurityValidator.validate_attachment(file_name, mime_type)
                self.assertTrue(result)

    def test_validate_attachment_dangerous_extensions(self):
        """Test de validation avec extensions dangereuses."""
        dangerous_files = [
            ("malware.exe", "application/octet-stream"),
            ("script.bat", "application/x-bat"),
            ("command.cmd", "application/x-cmd"),
            ("shell.sh", "application/x-sh"),
            ("powershell.ps1", "text/plain"),
            ("virus.vbs", "text/vbscript"),
            ("script.js", "application/javascript"),
            ("archive.jar", "application/java-archive"),
            ("library.dll", "application/x-msdownload"),
            ("library.so", "application/x-sharedlib"),
        ]

        for file_name, mime_type in dangerous_files:
            with self.subTest(file_name=file_name):
                with self.assertRaises(SecurityValidationError) as context:
                    SecurityValidator.validate_attachment(file_name, mime_type)
                self.assertIn("Extension de fichier non autorisée", str(context.exception))

    def test_validate_attachment_dangerous_mime_types(self):
        """Test de validation avec types MIME dangereux."""
        dangerous_mime_types = [
            ("file.bin", "application/x-msdownload"),
            ("file.bin", "application/x-executable"),
            ("file.bin", "application/x-dosexec"),
            ("file.swf", "application/x-shockwave-flash"),
        ]

        for file_name, mime_type in dangerous_mime_types:
            with self.subTest(mime_type=mime_type):
                with self.assertRaises(SecurityValidationError) as context:
                    SecurityValidator.validate_attachment(file_name, mime_type)
                self.assertIn("Type MIME non autorisé", str(context.exception))

        # Test séparé pour .jar car il a une extension dangereuse ET un MIME dangereux
        with self.assertRaises(SecurityValidationError) as context:
            SecurityValidator.validate_attachment("file.jar", "application/java-archive")
        # L'exception peut être pour l'extension ou le MIME type
        self.assertTrue(
            "Extension de fichier non autorisée" in str(context.exception) or
            "Type MIME non autorisé" in str(context.exception)
        )

    def test_validate_attachment_case_insensitive(self):
        """Test que la validation est insensible à la casse."""
        # Extension en majuscules
        with self.assertRaises(SecurityValidationError):
            SecurityValidator.validate_attachment("malware.EXE", "application/octet-stream")

        # Extension mixte
        with self.assertRaises(SecurityValidationError):
            SecurityValidator.validate_attachment("script.Bat", "application/x-bat")

    def test_sanitize_html_remove_scripts(self):
        """Test de suppression des scripts dans le HTML."""
        html_with_script = """
        <html>
            <body>
                <p>Safe content</p>
                <script>alert('malicious');</script>
                <p>More safe content</p>
            </body>
        </html>
        """

        result = SecurityValidator.sanitize_html(html_with_script)

        self.assertNotIn("<script>", result)
        self.assertNotIn("alert('malicious');", result)
        self.assertIn("Safe content", result)
        self.assertIn("More safe content", result)

    def test_sanitize_html_remove_event_handlers(self):
        """Test de suppression des gestionnaires d'événements."""
        html_with_events = """
        <div onclick="malicious()">Click me</div>
        <img src="image.jpg" onload="evil()" alt="Image">
        <a href="#" onmouseover="bad()">Link</a>
        """

        result = SecurityValidator.sanitize_html(html_with_events)

        self.assertNotIn("onclick", result)
        self.assertNotIn("onload", result)
        self.assertNotIn("onmouseover", result)
        self.assertIn("Click me", result)
        self.assertIn("Link", result)

    def test_sanitize_html_remove_javascript_links(self):
        """Test de suppression des liens javascript."""
        html_with_js_links = """
        <a href="javascript:alert('evil')">Evil link</a>
        <a href="https://example.com">Good link</a>
        <a href="javascript:void(0)">Another evil link</a>
        """

        result = SecurityValidator.sanitize_html(html_with_js_links)

        self.assertNotIn("javascript:", result)
        self.assertIn('href="#"', result)
        self.assertIn("https://example.com", result)
        self.assertIn("Evil link", result)
        self.assertIn("Good link", result)

    def test_sanitize_html_empty_input(self):
        """Test avec entrée vide."""
        self.assertEqual(SecurityValidator.sanitize_html(""), "")
        self.assertEqual(SecurityValidator.sanitize_html(None), "")


class TestTextProcessor(unittest.TestCase):
    """Tests pour la classe TextProcessor."""

    def test_html_to_plain_text_basic(self):
        """Test de conversion HTML vers texte basique."""
        html = "<p>Hello <strong>world</strong>!</p>"
        result = TextProcessor.html_to_plain_text(html)
        self.assertIn("Hello world!", result)
        self.assertNotIn("<p>", result)
        self.assertNotIn("<strong>", result)

    def test_html_to_plain_text_with_breaks(self):
        """Test de conversion avec sauts de ligne."""
        html = "<p>Line 1<br>Line 2</p><p>Paragraph 2</p>"
        result = TextProcessor.html_to_plain_text(html)
        self.assertIn("Line 1\nLine 2", result)

    def test_html_to_plain_text_empty(self):
        """Test avec HTML vide."""
        self.assertEqual(TextProcessor.html_to_plain_text(""), "")
        self.assertEqual(TextProcessor.html_to_plain_text(None), "")

    def test_chunk_text_basic(self):
        """Test de découpage de texte basique."""
        text = "This is a long text that needs to be chunked into smaller pieces for processing."
        chunks = TextProcessor.chunk_text(text, chunk_size=30, overlap=10)

        self.assertGreater(len(chunks), 1)
        self.assertLessEqual(len(chunks[0]), 30)

        # Vérifier le chevauchement
        if len(chunks) > 1:
            # Il devrait y avoir un chevauchement entre les chunks
            overlap_found = False
            for i in range(len(chunks) - 1):
                if chunks[i][-5:] in chunks[i + 1]:
                    overlap_found = True
                    break
            # Note: Le chevauchement peut ne pas être exact à cause de l'ajustement des mots

    def test_chunk_text_word_boundaries(self):
        """Test que le découpage respecte les limites de mots."""
        text = "word1 word2 word3 word4 word5 word6 word7 word8 word9 word10"
        chunks = TextProcessor.chunk_text(text, chunk_size=25, overlap=5)

        for chunk in chunks:
            # Vérifier qu'aucun chunk ne se termine au milieu d'un mot
            if chunk.strip() and not chunk.endswith(' '):
                # Le chunk devrait se terminer par un caractère de fin de mot
                self.assertTrue(chunk[-1].isspace() or chunk == text[text.rfind(chunk):text.rfind(chunk) + len(chunk)])

    def test_chunk_text_empty(self):
        """Test avec texte vide."""
        self.assertEqual(TextProcessor.chunk_text(""), [])
        self.assertEqual(TextProcessor.chunk_text(None), [])

    def test_chunk_text_short_text(self):
        """Test avec texte plus court que la taille de chunk."""
        text = "Short text"
        chunks = TextProcessor.chunk_text(text, chunk_size=100)

        self.assertEqual(len(chunks), 1)
        self.assertEqual(chunks[0], text)

    def test_generate_content_hash(self):
        """Test de génération de hash de contenu."""
        content1 = "This is test content"
        content2 = "This is test content"
        content3 = "This is different content"

        hash1 = TextProcessor.generate_content_hash(content1)
        hash2 = TextProcessor.generate_content_hash(content2)
        hash3 = TextProcessor.generate_content_hash(content3)

        # Les mêmes contenus doivent avoir le même hash
        self.assertEqual(hash1, hash2)

        # Des contenus différents doivent avoir des hash différents
        self.assertNotEqual(hash1, hash3)

        # Le hash doit être une chaîne hexadécimale de 64 caractères (SHA-256)
        self.assertEqual(len(hash1), 64)
        self.assertTrue(all(c in '0123456789abcdef' for c in hash1))

    def test_generate_content_hash_empty(self):
        """Test de génération de hash avec contenu vide."""
        self.assertEqual(TextProcessor.generate_content_hash(""), "")
        self.assertEqual(TextProcessor.generate_content_hash(None), "")


class TestRetryHandler(unittest.TestCase):
    """Tests pour la classe RetryHandler."""

    def setUp(self):
        """Configuration des tests."""
        self.config = RetryConfig(
            max_retries=3,
            initial_backoff=1.0,
            max_backoff=10.0,
            backoff_factor=2.0,
            jitter=False
        )
        self.handler = RetryHandler(self.config)

    def test_calculate_next_wait_time_exponential(self):
        """Test du calcul du temps d'attente avec backoff exponentiel."""
        # Première tentative
        wait1 = self.handler.calculate_next_wait_time(1)
        self.assertEqual(wait1, 1.0)  # initial_backoff

        # Deuxième tentative
        wait2 = self.handler.calculate_next_wait_time(2)
        self.assertEqual(wait2, 2.0)  # initial_backoff * backoff_factor^1

        # Troisième tentative
        wait3 = self.handler.calculate_next_wait_time(3)
        self.assertEqual(wait3, 4.0)  # initial_backoff * backoff_factor^2

    def test_calculate_next_wait_time_max_backoff(self):
        """Test que le temps d'attente ne dépasse pas le maximum."""
        # Tentative qui dépasserait le max_backoff
        wait = self.handler.calculate_next_wait_time(10)
        self.assertEqual(wait, self.config.max_backoff)

    def test_calculate_next_wait_time_with_jitter(self):
        """Test du calcul avec jitter activé."""
        config_with_jitter = RetryConfig(
            max_retries=3,
            initial_backoff=1.0,
            max_backoff=10.0,
            backoff_factor=2.0,
            jitter=True
        )
        handler_with_jitter = RetryHandler(config_with_jitter)

        # Avec jitter, le temps devrait varier légèrement
        wait1 = handler_with_jitter.calculate_next_wait_time(1)
        wait2 = handler_with_jitter.calculate_next_wait_time(1)

        # Les temps peuvent être différents à cause du jitter
        # Mais ils devraient être dans une plage raisonnable autour de 1.0
        self.assertGreater(wait1, 0.8)
        self.assertLess(wait1, 1.2)

    def test_should_retry_exception_rate_limit(self):
        """Test de détection des exceptions RateLimitExceededError."""
        exception = RateLimitExceededError("Rate limit exceeded", retry_after=60.0)
        should_retry, wait_time = RetryHandler.should_retry_exception(exception)

        self.assertTrue(should_retry)
        self.assertEqual(wait_time, 60.0)

    def test_should_retry_exception_api_error_retryable(self):
        """Test de détection des erreurs API temporaires."""
        retryable_status_codes = [429, 500, 502, 503, 504]

        for status_code in retryable_status_codes:
            with self.subTest(status_code=status_code):
                exception = APIError("API Error", status_code=status_code)
                should_retry, wait_time = RetryHandler.should_retry_exception(exception)

                self.assertTrue(should_retry)
                self.assertIsNone(wait_time)

    def test_should_retry_exception_api_error_non_retryable(self):
        """Test de détection des erreurs API non temporaires."""
        non_retryable_status_codes = [400, 401, 403, 404]

        for status_code in non_retryable_status_codes:
            with self.subTest(status_code=status_code):
                exception = APIError("API Error", status_code=status_code)
                should_retry, wait_time = RetryHandler.should_retry_exception(exception)

                self.assertFalse(should_retry)
                self.assertIsNone(wait_time)

    def test_should_retry_exception_network_errors(self):
        """Test de détection des erreurs réseau."""
        network_errors = [
            Exception("Connection timeout"),
            Exception("Connection refused"),
            Exception("Network timeout occurred"),
        ]

        for exception in network_errors:
            with self.subTest(exception=str(exception)):
                should_retry, wait_time = RetryHandler.should_retry_exception(exception)

                self.assertTrue(should_retry)
                self.assertIsNone(wait_time)

    def test_should_retry_exception_non_retryable(self):
        """Test avec exceptions non temporaires."""
        non_retryable_exceptions = [
            ValueError("Invalid value"),
            TypeError("Type error"),
            KeyError("Key not found"),
        ]

        for exception in non_retryable_exceptions:
            with self.subTest(exception=str(exception)):
                should_retry, wait_time = RetryHandler.should_retry_exception(exception)

                self.assertFalse(should_retry)
                self.assertIsNone(wait_time)


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Package de tests pour le système RAG Confluence.

Ce package contient tous les tests unitaires et d'intégration pour valider
le bon fonctionnement du système RAG Confluence.

Structure des tests:
- test_auth.py: Tests pour l'authentification
- test_config.py: Tests pour la configuration
- test_models.py: Tests pour les modèles de données
- test_storage.py: Tests pour le stockage
- test_exceptions.py: Tests pour les exceptions personnalisées
- test_constants.py: Tests pour les constantes
- test_security.py: Tests pour les utilitaires de sécurité
- test_utils.py: Tests pour les utilitaires généraux
- test_health_check.py: Tests pour les health checks
- test_log_security.py: Tests pour la sécurité des logs
- test_integration_security.py: Tests d'intégration de sécurité
- test_optimized_client.py: Tests pour le client optimisé
- test_parallel_pagination.py: Tests pour la pagination parallèle
- test_performance_optimization.py: Tests d'optimisation de performance
- test_thread_pool_optimization.py: Tests d'optimisation des thread pools
"""

import os
import sys
import logging

# Ajouter le répertoire parent au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configuration du logging pour les tests
logging.basicConfig(
    level=logging.WARNING,  # Réduire le bruit pendant les tests
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Désactiver les logs de certains modules externes pendant les tests
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('aiohttp').setLevel(logging.WARNING)
logging.getLogger('asyncio').setLevel(logging.WARNING)

__version__ = "1.0.0"
__author__ = "Profiling Team"

# Métadonnées des tests
TEST_MODULES = [
    "test_auth",
    "test_config",
    "test_models",
    "test_storage",
    "test_exceptions",
    "test_constants",
    "test_security",
    "test_utils",
    "test_health_check",
    "test_log_security",
    "test_integration_security",
    "test_optimized_client",
    "test_parallel_pagination",
    "test_performance_optimization",
    "test_thread_pool_optimization",
    # Tests critiques ajoutés
    "test_client",
    "test_orchestrator",
    "test_http",
    "test_pagination",
    "test_circuit_breaker",
    "test_tracking",
    "test_logging_utils",
    "test_main",
    # Tests du package processing
    "test_processing_enums",
    "test_document_extractors",
    "test_drawio_processor",
    "test_attachment_processor",
    "test_content_chunker",
    "test_content_retriever",
]

# Configuration des tests
TEST_CONFIG = {
    "timeout": 30,  # Timeout par défaut pour les tests async
    "temp_dir": "/tmp/confluence_rag_tests",
    "mock_data_dir": os.path.join(os.path.dirname(__file__), "mock_data"),
    "fixtures_dir": os.path.join(os.path.dirname(__file__), "fixtures"),
    "test_space_key": "TESTSPACE",
    "test_space_name": "Test Space for RAG",
}

def get_test_config():
    """Retourne la configuration des tests."""
    return TEST_CONFIG.copy()

def setup_test_environment():
    """Configure l'environnement de test."""
    # Créer le répertoire temporaire pour les tests
    os.makedirs(TEST_CONFIG["temp_dir"], exist_ok=True)

    # Configurer les variables d'environnement pour les tests
    os.environ.setdefault("CONFLUENCE_RAG_ENVIRONMENT", "test")
    os.environ.setdefault("LOG_LEVEL", "WARNING")
    os.environ.setdefault("SECURE_LOGGING", "true")

def cleanup_test_environment():
    """Nettoie l'environnement de test."""
    import shutil
    if os.path.exists(TEST_CONFIG["temp_dir"]):
        shutil.rmtree(TEST_CONFIG["temp_dir"], ignore_errors=True)

# Configuration automatique de l'environnement de test
setup_test_environment()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires pour le module des constantes.
"""

import unittest

from confluence.constants import APIConstants, AuthType


class TestAPIConstants(unittest.TestCase):
    """Tests pour la classe APIConstants."""

    def test_api_constants_endpoints(self):
        """Test des endpoints API."""
        # Vérifier que les endpoints sont définis
        self.assertIsInstance(APIConstants.CONTENT_ENDPOINT, str)
        self.assertIsInstance(APIConstants.SEARCH_ENDPOINT, str)

        # Vérifier les valeurs spécifiques
        self.assertEqual(APIConstants.CONTENT_ENDPOINT, "/wiki/rest/api/content")
        self.assertEqual(APIConstants.SEARCH_ENDPOINT, "/wiki/rest/api/search")

    def test_api_constants_timeouts(self):
        """Test des timeouts."""
        # Vérifier que les timeouts sont des entiers positifs
        self.assertIsInstance(APIConstants.DEFAULT_TIMEOUT, int)
        self.assertIsInstance(APIConstants.DEFAULT_RETRY_AFTER, int)

        self.assertGreater(APIConstants.DEFAULT_TIMEOUT, 0)
        self.assertGreater(APIConstants.DEFAULT_RETRY_AFTER, 0)

        # Vérifier les valeurs spécifiques
        self.assertEqual(APIConstants.DEFAULT_TIMEOUT, 60)
        self.assertEqual(APIConstants.DEFAULT_RETRY_AFTER, 60)

    def test_api_constants_page_sizes(self):
        """Test des tailles de page."""
        # Vérifier que les tailles de page sont des entiers positifs
        self.assertIsInstance(APIConstants.DEFAULT_PAGE_SIZE, int)
        self.assertIsInstance(APIConstants.MAX_PAGE_SIZE, int)
        self.assertIsInstance(APIConstants.ATTACHMENT_PAGE_SIZE, int)

        self.assertGreater(APIConstants.DEFAULT_PAGE_SIZE, 0)
        self.assertGreater(APIConstants.MAX_PAGE_SIZE, 0)
        self.assertGreater(APIConstants.ATTACHMENT_PAGE_SIZE, 0)

        # Vérifier la logique des tailles
        self.assertLessEqual(APIConstants.DEFAULT_PAGE_SIZE, APIConstants.MAX_PAGE_SIZE)

        # Vérifier les valeurs spécifiques
        self.assertEqual(APIConstants.DEFAULT_PAGE_SIZE, 50)
        self.assertEqual(APIConstants.MAX_PAGE_SIZE, 100)
        self.assertEqual(APIConstants.ATTACHMENT_PAGE_SIZE, 50)

    def test_api_constants_expand_fields(self):
        """Test des champs d'expansion."""
        # Vérifier que les champs d'expansion sont des listes
        self.assertIsInstance(APIConstants.STANDARD_EXPAND_FIELDS, list)
        self.assertIsInstance(APIConstants.ATTACHMENT_EXPAND_FIELDS, list)
        self.assertIsInstance(APIConstants.VERSION_EXPAND_FIELDS, list)

        # Vérifier que les champs contiennent les éléments attendus
        self.assertIn("body.storage", APIConstants.STANDARD_EXPAND_FIELDS)
        self.assertIn("version", APIConstants.STANDARD_EXPAND_FIELDS)
        self.assertIn("space", APIConstants.STANDARD_EXPAND_FIELDS)

        self.assertIn("children.attachment", APIConstants.ATTACHMENT_EXPAND_FIELDS)

        self.assertIn("version", APIConstants.VERSION_EXPAND_FIELDS)

    def test_api_constants_content_types(self):
        """Test des types de contenu."""
        # Vérifier que les types de contenu sont des chaînes
        self.assertIsInstance(APIConstants.JSON_CONTENT_TYPE, str)

        # Vérifier les valeurs spécifiques
        self.assertEqual(APIConstants.JSON_CONTENT_TYPE, "application/json")

    def test_api_constants_basic_structure(self):
        """Test de la structure de base des constantes API."""
        # Vérifier que la classe APIConstants existe et a des attributs
        self.assertTrue(hasattr(APIConstants, 'CONTENT_ENDPOINT'))
        self.assertTrue(hasattr(APIConstants, 'SEARCH_ENDPOINT'))
        self.assertTrue(hasattr(APIConstants, 'DEFAULT_TIMEOUT'))
        self.assertTrue(hasattr(APIConstants, 'JSON_CONTENT_TYPE'))

    def test_api_constants_immutability(self):
        """Test que les constantes ne peuvent pas être modifiées."""
        # Tenter de modifier une constante (cela ne devrait pas lever d'erreur
        # mais la valeur ne devrait pas changer si c'est bien implémenté)
        original_timeout = APIConstants.DEFAULT_TIMEOUT

        # En Python, on ne peut pas vraiment empêcher la modification des attributs de classe
        # mais on peut vérifier que les valeurs sont cohérentes
        self.assertEqual(APIConstants.DEFAULT_TIMEOUT, original_timeout)


class TestAuthType(unittest.TestCase):
    """Tests pour l'énumération AuthType."""

    def test_auth_type_values(self):
        """Test des valeurs de l'énumération AuthType."""
        # Vérifier que les valeurs attendues existent
        self.assertEqual(AuthType.PAT_TOKEN.value, "pat_token")
        self.assertEqual(AuthType.API_TOKEN.value, "api_token")

    def test_auth_type_members(self):
        """Test des membres de l'énumération AuthType."""
        # Vérifier que tous les membres attendus sont présents
        expected_members = {"PAT_TOKEN", "API_TOKEN"}
        actual_members = {member.name for member in AuthType}

        self.assertEqual(actual_members, expected_members)

    def test_auth_type_iteration(self):
        """Test de l'itération sur l'énumération AuthType."""
        auth_types = list(AuthType)

        self.assertEqual(len(auth_types), 2)
        self.assertIn(AuthType.PAT_TOKEN, auth_types)
        self.assertIn(AuthType.API_TOKEN, auth_types)

    def test_auth_type_comparison(self):
        """Test de comparaison des valeurs d'énumération."""
        # Vérifier que les comparaisons fonctionnent correctement
        self.assertEqual(AuthType.PAT_TOKEN, AuthType.PAT_TOKEN)
        self.assertNotEqual(AuthType.PAT_TOKEN, AuthType.API_TOKEN)

    def test_auth_type_string_representation(self):
        """Test de la représentation en chaîne des valeurs d'énumération."""
        self.assertEqual(str(AuthType.PAT_TOKEN), "AuthType.PAT_TOKEN")
        self.assertEqual(str(AuthType.API_TOKEN), "AuthType.API_TOKEN")

    def test_auth_type_value_access(self):
        """Test d'accès aux valeurs de l'énumération."""
        # Vérifier qu'on peut accéder aux valeurs par nom
        self.assertEqual(AuthType["PAT_TOKEN"], AuthType.PAT_TOKEN)
        self.assertEqual(AuthType["API_TOKEN"], AuthType.API_TOKEN)

        # Vérifier qu'on peut accéder aux valeurs par valeur
        self.assertEqual(AuthType("pat_token"), AuthType.PAT_TOKEN)
        self.assertEqual(AuthType("api_token"), AuthType.API_TOKEN)

    def test_auth_type_invalid_value(self):
        """Test d'erreur avec valeur invalide."""
        with self.assertRaises(ValueError):
            AuthType("invalid_auth_type")

    def test_auth_type_invalid_member(self):
        """Test d'erreur avec membre invalide."""
        with self.assertRaises(KeyError):
            AuthType["INVALID_MEMBER"]


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests d'intégration pour la sécurité des logs.
"""

import unittest
import logging
import io
import tempfile
import os

from confluence.logging_utils import SecurityFilter, CorrelationIdFilter, StructuredLogFormatter


class TestIntegrationSecurity(unittest.TestCase):
    """Tests d'intégration pour la sécurité des logs."""

    def setUp(self):
        """Configuration des tests."""
        self.temp_dir = tempfile.mkdtemp()
        self.log_file = os.path.join(self.temp_dir, "test_security.log")

    def tearDown(self):
        """Nettoyage après les tests."""
        if os.path.exists(self.log_file):
            os.remove(self.log_file)
        os.rmdir(self.temp_dir)

    def test_manual_logging_setup_with_security(self):
        """Test de la configuration manuelle du logging avec sécurité."""
        # Créer les filtres manuellement
        security_filter = SecurityFilter()
        correlation_filter = CorrelationIdFilter()

        # Créer un formateur
        formatter = logging.Formatter('%(levelname)s - [%(correlation_id)s] - %(message)s')

        # Créer un handler de fichier
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setFormatter(formatter)
        file_handler.addFilter(security_filter)
        file_handler.addFilter(correlation_filter)

        # Configurer le logger
        logger = logging.getLogger("test_integration")
        logger.setLevel(logging.INFO)
        logger.addHandler(file_handler)

        # Logger un message avec des informations sensibles
        logger.info("Test avec token ABC123XYZ456789012345")

        # Fermer le handler pour s'assurer que le fichier est écrit
        file_handler.close()
        logger.removeHandler(file_handler)

        # Lire le fichier de log
        with open(self.log_file, 'r') as f:
            log_content = f.read()

        # Vérifier que le token a été masqué
        self.assertNotIn("ABC123XYZ456789012345", log_content)
        self.assertIn("***TOKEN***", log_content)

    def test_structured_logging_with_security(self):
        """Test du logging structuré avec sécurité."""
        # Créer les filtres
        security_filter = SecurityFilter()
        correlation_filter = CorrelationIdFilter()

        # Créer un formateur structuré
        formatter = StructuredLogFormatter(include_traceback=True)

        # Créer un handler de fichier
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setFormatter(formatter)
        file_handler.addFilter(security_filter)
        file_handler.addFilter(correlation_filter)

        # Configurer le logger
        logger = logging.getLogger("test_structured")
        logger.setLevel(logging.INFO)
        logger.addHandler(file_handler)

        # Logger un message avec des informations sensibles dans les champs extra
        logger.info("Authentification", extra={
            "token": "ABC123XYZ456789012345DEFGHIJKLMNOP",
            "user": "john",
            "api_key": "SECRET123456789012345ABCDEFGHIJK"
        })

        # Fermer le handler
        file_handler.close()
        logger.removeHandler(file_handler)

        # Lire le fichier de log
        with open(self.log_file, 'r') as f:
            log_content = f.read()

        # Vérifier que les tokens ont été masqués
        self.assertNotIn("ABC123XYZ456789012345DEFGHIJKLMNOP", log_content)
        self.assertNotIn("SECRET123456789012345ABCDEFGHIJK", log_content)
        self.assertIn("***TOKEN***", log_content)
        # L'utilisateur ne doit pas être masqué
        self.assertIn("john", log_content)

    def test_confluence_client_error_sanitization(self):
        """Test de la sécurisation des erreurs du ConfluenceClient."""
        from confluence.security import SecurityUtils

        # Tester la méthode de nettoyage directement avec un token assez long
        error_message = "Authentication failed with token ABC123XYZ456789012345DEFGHIJKLMNOP"
        sanitized = SecurityUtils.sanitize_error_message(error_message)

        # Vérifier que le token a été masqué
        self.assertNotIn("ABC123XYZ456789012345DEFGHIJKLMNOP", sanitized)
        self.assertIn("***TOKEN***", sanitized)

    def test_multiple_filters_integration(self):
        """Test de l'intégration de plusieurs filtres."""
        # Créer les filtres
        security_filter = SecurityFilter()
        correlation_filter = CorrelationIdFilter()

        # Créer un formateur
        formatter = logging.Formatter('%(levelname)s - [%(correlation_id)s] - %(message)s')

        # Créer un handler en mémoire
        log_stream = io.StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setFormatter(formatter)
        handler.addFilter(security_filter)
        handler.addFilter(correlation_filter)

        # Créer et configurer le logger
        logger = logging.getLogger("test_multiple_filters")
        logger.setLevel(logging.INFO)
        logger.addHandler(handler)

        # Logger un message avec des informations sensibles
        logger.info("Connexion avec token ABC123XYZ456789012345")

        # Vérifier le résultat
        log_output = log_stream.getvalue()
        self.assertNotIn("ABC123XYZ456789012345", log_output)
        self.assertIn("***TOKEN***", log_output)
        self.assertIn("no-correlation-id", log_output) # Default from CorrelationIdFilter

    def test_security_with_exception_logging(self):
        """Test de la sécurité avec le logging d'exceptions."""
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests pour le client Confluence.
"""

import unittest
import asyncio
import aiohttp
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

from confluence.client import ConfluenceClient
from confluence.config import ConfluenceConfig, SearchCriteria
from confluence.models import ContentItem, AttachmentDetail
from confluence.exceptions import (
    AuthenticationError, APIError, ContentNotFoundError, RateLimitExceededError
)
from confluence.constants import APIConstants


class TestConfluenceClient(unittest.TestCase):
    """Tests pour la classe ConfluenceClient."""

    def setUp(self):
        """Configuration des tests."""
        self.config = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token="test_token",
            timeout=30
        )
        self.client = ConfluenceClient(self.config)

    def tearDown(self):
        """Nettoyage après les tests."""
        # Fermer la session si elle existe
        if hasattr(self.client, '_session') and self.client._session:
            asyncio.run(self.client.close())

    def test_client_initialization(self):
        """Test de l'initialisation du client."""
        self.assertEqual(self.client.config, self.config)
        self.assertIsNotNone(self.client.logger)
        self.assertIsNotNone(self.client.auth_manager)
        self.assertIsNotNone(self.client.response_processor)
        self.assertIsNotNone(self.client.circuit_breaker)
        self.assertIsNotNone(self.client.retry_config)
        self.assertIsNotNone(self.client.thread_pool_manager)
        self.assertIsNone(self.client._session)

    def test_session_timeout_configuration(self):
        """Test de la configuration du timeout de session."""
        # Test avec timeout configuré
        self.assertEqual(self.client._session_timeout.total, 30)

        # Test avec timeout par défaut (utiliser la valeur par défaut)
        config_default_timeout = ConfluenceConfig(
            url="https://test.atlassian.net",
            username="<EMAIL>",
            api_token="test_token"
            # timeout utilise la valeur par défaut
        )
        client_default_timeout = ConfluenceClient(config_default_timeout)
        # Vérifier que le timeout par défaut est utilisé
        self.assertGreater(client_default_timeout._session_timeout.total, 0)

    @patch('aiohttp.ClientSession')
    @pytest.mark.asyncio
    async def test_get_session_creation(self, mock_session_class):
        """Test de la création de session."""
        mock_session = AsyncMock()
        mock_session.closed = False
        mock_session_class.return_value = mock_session

        # Mock auth manager
        self.client.auth_manager.get_auth_headers_and_auth = Mock(
            return_value=({'Authorization': 'Bearer token'}, None)
        )

        session = await self.client._get_session()

        self.assertEqual(session, mock_session)
        mock_session_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_session_reuse(self):
        """Test de la réutilisation de session."""
        # Créer une session mock
        mock_session = AsyncMock()
        mock_session.closed = False
        self.client._session = mock_session

        session = await self.client._get_session()

        self.assertEqual(session, mock_session)

    @pytest.mark.asyncio
    async def test_close_session(self):
        """Test de la fermeture de session."""
        # Créer une session mock
        mock_session = AsyncMock()
        mock_session.closed = False
        self.client._session = mock_session

        await self.client._close_session()

        mock_session.close.assert_called_once()
        self.assertIsNone(self.client._session)

    @pytest.mark.asyncio
    async def test_context_manager(self):
        """Test du context manager."""
        with patch.object(self.client, '_get_session') as mock_get_session:
            with patch.object(self.client, '_close_session') as mock_close_session:
                mock_get_session.return_value = AsyncMock()

                async with self.client as client:
                    self.assertEqual(client, self.client)

                mock_get_session.assert_called_once()
                mock_close_session.assert_called_once()

    @patch('confluence_rag.client.ConfluenceClient._make_api_request')
    @pytest.mark.asyncio
    async def test_make_cql_request(self, mock_api_request):
        """Test de la requête CQL."""
        mock_response = {
            "results": [{"id": "123", "title": "Test Page"}],
            "size": 1
        }
        mock_api_request.return_value = mock_response

        result = await self.client._make_cql_request(
            cql="space=TEST",
            start=0,
            limit=50,
            expand="body.storage"
        )

        self.assertEqual(result, mock_response)
        mock_api_request.assert_called_once_with(
            method="GET",
            endpoint=APIConstants.SEARCH_ENDPOINT,
            service_name="confluence_cql_search",
            params={
                "cql": "space=TEST",
                "start": 0,
                "limit": 50,
                "expand": "body.storage"
            }
        )

    @patch('confluence_rag.client.ConfluenceClient._make_api_request')
    @pytest.mark.asyncio
    async def test_make_cql_request_invalid_response(self, mock_api_request):
        """Test de la requête CQL avec réponse invalide."""
        mock_api_request.return_value = "invalid response"

        with self.assertRaises(APIError):
            await self.client._make_cql_request("space=TEST")

    def test_build_expand_string(self):
        """Test de la construction de la chaîne d'expansion."""
        criteria = SearchCriteria(
            spaces=["TEST"],
            include_attachments=True,
            include_children=True
        )

        expand_string = self.client._build_expand_string(criteria)

        # Vérifier que les champs essentiels sont présents
        self.assertIn("body.storage", expand_string)
        self.assertIn("version", expand_string)
        self.assertIn("space", expand_string)

        # Vérifier les champs conditionnels
        if criteria.include_attachments:
            self.assertIn("children.attachment", expand_string)

        # Test sans attachments pour vérifier la différence
        criteria_no_attachments = SearchCriteria(
            spaces=["TEST"],
            include_attachments=False
        )
        expand_string_no_attachments = self.client._build_expand_string(criteria_no_attachments)
        self.assertNotIn("children.attachment", expand_string_no_attachments)

    @patch('confluence_rag.client.ConfluenceClient._make_cql_request')
    @pytest.mark.asyncio
    async def test_estimate_total_results(self, mock_cql_request):
        """Test de l'estimation du nombre total de résultats."""
        mock_cql_request.return_value = {"size": 150}

        total = await self.client._estimate_total_results("space=TEST")

        self.assertEqual(total, 150)
        mock_cql_request.assert_called_once_with(
            "space=TEST", start=0, limit=1, expand=""
        )

    def test_calculate_effective_limits(self):
        """Test du calcul des limites effectives."""
        # Test avec limite inférieure à l'estimation
        max_results, page_size = self.client._calculate_effective_limits(1000, 500)
        self.assertEqual(max_results, 500)
        self.assertGreater(page_size, 0)

        # Test avec limite supérieure à l'estimation
        max_results, page_size = self.client._calculate_effective_limits(100, 500)
        self.assertEqual(max_results, 100)
        self.assertGreater(page_size, 0)

    @patch('confluence_rag.client.ConfluenceClient._search_content_parallel')
    @pytest.mark.asyncio
    async def test_search_content(self, mock_search_parallel):
        """Test de la recherche de contenu."""
        # Utiliser des mocks simples au lieu d'instances ContentItem complètes
        mock_content_items = [Mock(id="123", title="Test Page", type="page")]
        mock_search_parallel.return_value = mock_content_items

        criteria = SearchCriteria(spaces=["TEST"], max_results=10)
        results = await self.client.search_content(criteria)

        self.assertEqual(results, mock_content_items)
        mock_search_parallel.assert_called_once_with(criteria)

    @patch('confluence_rag.client.ConfluenceClient._make_api_request')
    @pytest.mark.asyncio
    async def test_get_content_success(self, mock_api_request):
        """Test de récupération de contenu réussie."""
        # Mock d'une réponse complète de l'API Confluence
        mock_response = {
            "id": "123",
            "title": "Test Page",
            "type": "page",
            "status": "current",
            "space": {"id": "1", "key": "TEST", "name": "Test Space", "type": "global"},
            "version": {"number": 1},
            "created": "2023-01-01T00:00:00.000Z",
            "creator": {"id": "user1", "username": "testuser", "display_name": "Test User"},
            "lastUpdated": "2023-01-01T00:00:00.000Z",
            "lastUpdater": {"id": "user1", "username": "testuser", "display_name": "Test User"},
            "_links": {
                "webui": "/pages/viewpage.action?pageId=123",
                "self": "/rest/api/content/123"
            },
            "body": {"storage": {"value": "<p>Content</p>"}}
        }
        mock_api_request.return_value = mock_response

        # Mock la méthode de conversion pour éviter les problèmes de validation
        with patch('confluence_rag.client.ContentItem') as mock_content_item:
            mock_instance = Mock()
            mock_instance.id = "123"
            mock_instance.title = "Test Page"
            mock_content_item.return_value = mock_instance

            result = await self.client.get_content("123")

            self.assertEqual(result.id, "123")
            self.assertEqual(result.title, "Test Page")

    @patch('confluence_rag.client.ConfluenceClient._make_api_request')
    @pytest.mark.asyncio
    async def test_get_content_not_found(self, mock_api_request):
        """Test de récupération de contenu non trouvé."""
        mock_api_request.side_effect = ContentNotFoundError("Content not found")

        with self.assertRaises(ContentNotFoundError):
            await self.client.get_content("nonexistent")

    @patch('confluence_rag.client.ConfluenceClient._make_api_request')
    @pytest.mark.asyncio
    async def test_download_attachment_success(self, mock_api_request):
        """Test de téléchargement de pièce jointe réussi."""
        mock_data = b"file content"
        mock_api_request.return_value = mock_data

        attachment = AttachmentDetail(
            id="att123",
            file_name="test.pdf",
            download_url="https://test.com/download/att123"
        )

        result = await self.client.download_attachment(attachment)

        self.assertEqual(result, mock_data)
        mock_api_request.assert_called_once_with(
            method="GET",
            endpoint="https://test.com/download/att123",
            service_name="confluence_attachment_download",
            download_mode=True,
            retry_decorator=self.client.download_retry
        )

    @pytest.mark.asyncio
    async def test_download_attachment_no_url(self):
        """Test de téléchargement de pièce jointe sans URL."""
        attachment = AttachmentDetail(
            id="att123",
            file_name="test.pdf",
            download_url=None
        )

        with self.assertRaises(ValueError):
            await self.client.download_attachment(attachment)


if __name__ == '__main__':
    unittest.main()

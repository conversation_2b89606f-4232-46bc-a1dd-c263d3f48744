#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Authentication management for the Confluence  client.
"""

import logging
import aiohttp
from typing import Dict, Optional, Tuple

from .config import ConfluenceConfig
from .constants import AuthType, APIConstants
from .exceptions import AuthenticationError


class AuthenticationManager:
    """Manages authentication configuration and setup."""

    def __init__(self, config: ConfluenceConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self._auth_type = self._determine_auth_type()

    def _determine_auth_type(self) -> AuthType:
        """Determine the authentication type from configuration."""
        pat_token = self.config.pat_token.get_secret_value() if self.config.pat_token else None
        api_token = self.config.api_token.get_secret_value() if self.config.api_token else None

        if pat_token:
            self.logger.info("Configuration pour Personal Access Token (PAT) détectée.")
            return AuthType.PAT_TOKEN
        elif api_token and self.config.username:
            self.logger.info("Configuration pour API token classique (username/api_token) détectée.")
            return AuthType.API_TOKEN
        else:
            raise AuthenticationError(
                "Configuration d'authentification invalide: "
                "fournissez soit un PAT token, soit un username et API token."
            )

    def get_auth_headers_and_auth(self) -> Tuple[Dict[str, str], Optional[aiohttp.BasicAuth]]:
        """Get authentication headers and BasicAuth object."""
        headers: Dict[str, str] = {"Accept": APIConstants.JSON_CONTENT_TYPE}
        auth: Optional[aiohttp.BasicAuth] = None

        if self._auth_type == AuthType.PAT_TOKEN:
            pat_token = self.config.pat_token.get_secret_value()
            headers["Authorization"] = f"Bearer {pat_token}"
            self.logger.debug("Utilisation de l'authentification PAT.")
        elif self._auth_type == AuthType.API_TOKEN:
            api_token = self.config.api_token.get_secret_value()
            auth = aiohttp.BasicAuth(self.config.username, api_token)
            self.logger.debug("Utilisation de l'authentification Basic (API Token).")

        return headers, auth

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Récupération et orchestration de contenu pour le système RAG de Confluence.
"""

import logging
import asyncio
from typing import List, Dict, Optional

from .attachment_processor import AttachmentProcessor
from .content_chunker import ContentChunker
from ..models import ContentItem
from ..client import ConfluenceClient
from ..config import SearchCriteria, ProcessingConfig
from ..utils import TextProcessor
from ..exceptions import ContentProcessingError


class ContentRetriever:
    """Récupérateur de contenu Confluence amélioré avec une meilleure gestion des erreurs et un suivi de la progression."""

    def __init__(self, client: ConfluenceClient, processing_config: Optional[ProcessingConfig] = None, storage_config=None):
        """
        Initialise le récupérateur avec le client Confluence et la configuration de traitement.

        Args:
            client: Client Confluence pour les appels d'API
            processing_config: Configuration de traitement (optionnel)
            storage_config: Configuration de stockage pour la gestion des pièces jointes (optionnel)
        """

        self.client = client
        self.logger = logging.getLogger(__name__)

        # Use provided configuration or create default
        self.config = processing_config or ProcessingConfig.from_env()

        # Initialize components
        self.attachment_processor = AttachmentProcessor(client, self.config, storage_config)
        self.chunker = ContentChunker(self.config.chunk_size, self.config.overlap_size)

        # Track retrieval statistics
        self._stats = {
            "content_retrieved": 0,
            "attachments_processed": 0,
            "errors": 0
        }

        self.logger.info(
            f"ContentRetriever initialized with chunk_size={self.config.chunk_size}, "
            f"overlap_size={self.config.overlap_size}"
        )

    async def retrieve_content(self, content_id: str, process_attachments: bool = True) -> ContentItem:
        """
        Récupère le contenu Confluence avec ses pièces jointes.

        Args:
            content_id: ID du contenu à récupérer
            process_attachments: Indique s'il faut traiter les pièces jointes

        Returns:
            ContentItem avec le contenu et les pièces jointes traités
        """

        try:
            self.logger.info(f"Retrieving content {content_id}")

            # Retrieve content
            content_item = await self.client.get_content(content_id)

            # Extract plain text from HTML body
            if content_item.body_view:
                content_item.body_plain = TextProcessor.html_to_plain_text(content_item.body_view)
                self.logger.debug(f"Extracted {len(content_item.body_plain)} characters of plain text")

            # Process attachments if requested
            if process_attachments:
                await self._process_content_attachments(content_item)

            # Create text chunks
            content_item.processed_chunks = self.chunker.create_chunks(content_item)
            self.logger.debug(f"Created {len(content_item.processed_chunks)} text chunks")

            self._stats["content_retrieved"] += 1
            self.logger.info(f"Successfully retrieved content {content_id} - {content_item.title}")

            return content_item

        except Exception as e:
            self._stats["errors"] += 1
            self.logger.error(f"Content retrieval failed for {content_id}: {e}")

            if isinstance(e, ContentProcessingError):
                raise
            else:
                raise ContentProcessingError(
                    f"Content retrieval failed: {e}",
                    content_id=content_id
                )

    async def _process_content_attachments(self, content_item: ContentItem) -> None:
        """Traite les pièces jointes d'un élément de contenu."""
        try:
            attachments = await self.client.get_attachments(content_item.id)
            if not attachments:
                self.logger.debug(f"No attachments found for content {content_item.id}")
                return

            self.logger.info(f"Processing {len(attachments)} attachments for content {content_item.id}")

            # Process attachments in parallel with error handling
            processed_attachments = await asyncio.gather(
                *[self.attachment_processor.process_attachment(attachment) for attachment in attachments],
                return_exceptions=True
            )

            # Filter successful results and log errors
            successful_attachments = []
            for i, result in enumerate(processed_attachments):
                if isinstance(result, Exception):
                    self.logger.error(f"Attachment processing failed: {result}")
                    self._stats["errors"] += 1
                else:
                    successful_attachments.append(result)
                    self._stats["attachments_processed"] += 1

            content_item.attachments = successful_attachments
            self.logger.info(
                f"Processed {len(successful_attachments)}/{len(attachments)} attachments successfully "
                f"for content {content_item.id}"
            )

        except Exception as e:
            self.logger.error(f"Attachment processing failed for content {content_item.id}: {e}")
            # Don't fail the entire content retrieval for attachment errors
            content_item.attachments = []

    def get_retrieval_stats(self) -> Dict[str, int]:
        """Get retrieval statistics."""
        attachment_stats = self.attachment_processor.get_processing_stats()
        return {
            **self._stats,
            **{f"attachment_{k}": v for k, v in attachment_stats.items()}
        }

    def reset_stats(self) -> None:
        """Reset all statistics."""
        self._stats = {
            "content_retrieved": 0,
            "attachments_processed": 0,
            "errors": 0
        }
        self.attachment_processor.reset_stats()

    async def search_and_retrieve(
            self,
            criteria: SearchCriteria,
            process_attachments: bool = True
    ) -> List[ContentItem]:
        """
        Recherche et récupère du contenu selon les critères spécifiés.

        Args:
            criteria: Critères de recherche
            process_attachments: Indique s'il faut traiter les pièces jointes

        Returns:
            Liste d'objets ContentItem avec le contenu traité
        """
        try:
            self.logger.info(f"Starting search and retrieval with criteria: {criteria}")

            # Search for content
            content_items = await self.client.search_content(criteria)
            self.logger.info(f"Found {len(content_items)} content items")

            if not content_items:
                return []

            # Process each content item
            detailed_items = []
            for i, item in enumerate(content_items, 1):
                try:
                    self.logger.info(f"Processing content {i}/{len(content_items)}: {item.id} - {item.title}")

                    detailed_item = await self.retrieve_content(item.id, process_attachments)
                    detailed_items.append(detailed_item)

                    # Process children recursively if requested
                    if criteria.include_children and detailed_item.children:
                        child_items = await self._retrieve_children_recursively(
                            detailed_item,
                            criteria,
                            process_attachments,
                            current_depth=1
                        )
                        detailed_items.extend(child_items)

                except Exception as e:
                    self.logger.error(f"Failed to process content {item.id}: {e}")
                    self._stats["errors"] += 1
                    continue

            self.logger.info(f"Search and retrieval completed: {len(detailed_items)} items processed")
            return detailed_items

        except Exception as e:
            self.logger.error(f"Search and retrieval failed: {e}")
            raise

    async def _retrieve_children_recursively(
            self,
            parent: ContentItem,
            criteria: SearchCriteria,
            process_attachments: bool,
            current_depth: int
    ) -> List[ContentItem]:
        """
        Récupère de manière récursive les pages enfants jusqu'à la profondeur spécifiée.

        Args:
            parent: Élément de contenu parent
            criteria: Critères de recherche
            process_attachments: Indique s'il faut traiter les pièces jointes
            current_depth: Profondeur de récursion actuelle

        Returns:
            Liste d'objets ContentItem enfants
        """
        if current_depth > criteria.max_children_depth:
            self.logger.debug(f"Max depth {criteria.max_children_depth} reached, stopping recursion")
            return []

        self.logger.info(
            f"Retrieving children of {parent.id} - {parent.title} "
            f"(depth {current_depth}/{criteria.max_children_depth})"
        )

        child_items = []
        child_count = len(parent.children)

        if child_count == 0:
            return child_items

        self.logger.info(f"Processing {child_count} child pages")

        for i, child_info in enumerate(parent.children, 1):
            try:
                child_id = child_info.get('id')
                if not child_id:
                    self.logger.warning(f"Missing ID for child of {parent.id}")
                    continue

                child_title = child_info.get('title', 'Untitled')
                self.logger.debug(f"Processing child {i}/{child_count}: {child_id} - {child_title}")

                # Retrieve child content
                child_item = await self.retrieve_content(child_id, process_attachments)
                child_items.append(child_item)

                # Recursively process grandchildren
                if child_item.children and current_depth < criteria.max_children_depth:
                    grandchildren = await self._retrieve_children_recursively(
                        child_item,
                        criteria,
                        process_attachments,
                        current_depth + 1
                    )
                    child_items.extend(grandchildren)

            except Exception as e:
                self.logger.error(f"Failed to process child {child_info.get('id', 'unknown')}: {e}")
                self._stats["errors"] += 1
                continue

        self.logger.info(f"Completed children retrieval for {parent.title}: {len(child_items)} items")
        return child_items

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Client API Confluence et constructeur de requêtes CQL.
(Version custom sans dépendance directe à atlassian-python-api pour les appels HTTP)
"""

import logging
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional, Union, Callable, TypeVar, Tuple

from .config import ConfluenceConfig, SearchCriteria
from .models import ContentItem, AttachmentDetail
from .utils import RetryHandler
from .circuit_breaker import CircuitBreaker
from .thread_pool_manager import get_thread_pool_manager
from .exceptions import (
    AuthenticationError, APIError, ContentNotFoundError, RateLimitExceededError
)
from .constants import APIConstants
from .auth import AuthenticationManager
from .security import SecurityUtils
from .http import ResponseProcessor, RequestContext
from .pagination import PaginationCalculator

# Définir un type générique pour les retours de fonction
T = TypeVar('T')





class ConfluenceClient:
    """Client pour l'API Confluence, utilisant aiohttp pour les appels directs."""

    def __init__(self, config: ConfluenceConfig):
        """Initialise le client avec la configuration fournie."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize components
        self.auth_manager = AuthenticationManager(config)
        self.response_processor = ResponseProcessor(self.logger)
        self.circuit_breaker = CircuitBreaker(config.circuit_breaker_config)
        self.retry_config = config.retry_config
        self.thread_pool_manager = get_thread_pool_manager()

        # Session management
        self._session: Optional[aiohttp.ClientSession] = None
        effective_timeout = config.timeout if config.timeout and config.timeout > 0 else APIConstants.DEFAULT_TIMEOUT
        self._session_timeout = aiohttp.ClientTimeout(total=effective_timeout)

        self._log_configuration()
        self._configure_decorators()

    def _log_configuration(self):
        """Log the current configuration."""
        self.logger.info(
            f"Configuration de retry: max_retries={self.retry_config.max_retries}, "
            f"initial_backoff={self.retry_config.initial_backoff}s, "
            f"max_backoff={self.retry_config.max_backoff}s"
        )

    def _configure_decorators(self):
        """Configure les décorateurs avec les paramètres de retry."""
        self.standard_retry = RetryHandler.async_retry(
            retry_config=self.retry_config,
            retry_on_exceptions=(APIError, RateLimitExceededError),
            max_tries=self.retry_config.max_retries
        )

        self.download_retry = RetryHandler.async_retry(
            retry_config=self.retry_config,
            retry_on_exceptions=(
                APIError, RateLimitExceededError,
                aiohttp.ClientError, asyncio.TimeoutError
            ),
            max_tries=self.retry_config.max_retries
        )

    async def _get_session(self) -> aiohttp.ClientSession:
        """Obtient ou crée une session HTTP aiohttp réutilisable."""
        if self._session is None or self._session.closed:
            headers, auth = self.auth_manager.get_auth_headers_and_auth()

            self._session = aiohttp.ClientSession(
                timeout=self._session_timeout,
                headers=headers,
                auth=auth
            )
            self.logger.info("Nouvelle session aiohttp créée.")
        return self._session

    async def _make_api_request(
            self,
            method: str,
            endpoint: str,
            service_name: str,
            retry_decorator: Optional[Callable] = None,
            process_response: Optional[Callable[[Union[Dict[str, Any], List[Any]]], T]] = None,
            download_mode: bool = False,
            **kwargs
    ) -> T:
        """Méthode générique pour effectuer des appels API avec gestion des erreurs."""
        context = RequestContext(
            method=method,
            endpoint=endpoint,
            service_name=service_name,
            download_mode=download_mode,
            headers=kwargs.get('headers', {}),
            params=kwargs.get('params', {})
        )

        # Configure circuit breaker exceptions
        circuit_exceptions = (APIError, RateLimitExceededError)
        if download_mode:
            circuit_exceptions += (aiohttp.ClientError, asyncio.TimeoutError)

        circuit_breaker_decorator = self.circuit_breaker.circuit_breaker(
            service_name=service_name,
            exceptions_to_trip=circuit_exceptions
        )

        # Select retry decorator
        effective_retry_decorator = (
                retry_decorator or
                (self.download_retry if download_mode else self.standard_retry)
        )

        async def _execute_request():
            return await self._execute_http_request(context, process_response, **kwargs)

        decorated_execute = circuit_breaker_decorator(
            effective_retry_decorator(_execute_request)
        )
        return await decorated_execute()

    async def _execute_http_request(
            self,
            context: RequestContext,
            process_response: Optional[Callable] = None,
            **kwargs
    ) -> Any:
        """Execute the actual HTTP request."""
        full_url = self._build_full_url(context.endpoint)
        session = await self._get_session()

        # Prepare headers
        req_headers = self._prepare_request_headers(context, kwargs)

        try:
            self.logger.debug(
                f"Making {context.method} request to {full_url} "
                f"with params: {context.params}, headers: {req_headers}"
            )

            async with session.request(
                    context.method, full_url, headers=req_headers, **kwargs
            ) as response:
                result = await self.response_processor.process_response(response, context)

                if process_response and not context.download_mode:
                    if isinstance(result, (dict, list)):
                        return process_response(result)
                    else:
                        self.logger.warning(
                            f"process_response expected Dict/List, got {type(result)} "
                            f"for {full_url}. Result preview: {str(result)[:200]}"
                        )

                return result

        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            sanitized_error = SecurityUtils.sanitize_error_message(str(e))
            self.logger.error(
                f"Erreur réseau lors de l'appel à {full_url}: {sanitized_error}",
                exc_info=True
            )
            raise APIError(f"Erreur réseau: {sanitized_error}") from e
        except (AuthenticationError, ContentNotFoundError, RateLimitExceededError, APIError):
            raise
        except Exception as e:
            sanitized_error = SecurityUtils.sanitize_error_message(str(e))
            self.logger.error(
                f"Erreur inattendue lors de l'appel à {full_url}: {sanitized_error}",
                exc_info=True
            )
            raise APIError(f"Erreur inattendue: {sanitized_error}") from e

    def _build_full_url(self, endpoint: str) -> str:
        """Build the full URL for the API request."""
        if endpoint.startswith('http'):
            return endpoint
        return f"{self.config.url.rstrip('/')}{endpoint}"

    def _prepare_request_headers(
            self,
            context: RequestContext,
            kwargs: Dict[str, Any]
    ) -> Dict[str, str]:
        """Prepare headers for the HTTP request."""
        req_headers = kwargs.pop('headers', {}).copy()

        if (context.method.upper() in ["POST", "PUT", "PATCH"] and
                'json' in kwargs and
                'Content-Type' not in req_headers):
            req_headers['Content-Type'] = APIConstants.JSON_CONTENT_TYPE

        return req_headers

    async def _close_session(self):
        """Close the HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
            self.logger.info("Session aiohttp fermée.")

    async def close(self):
        """Ferme explicitement la session aiohttp du client."""
        await self._close_session()

    async def __aenter__(self):
        await self._get_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._close_session()

    async def _make_cql_request(
            self,
            cql: str,
            start: int = 0,
            limit: int = 100,
            expand: Optional[str] = None
    ) -> Dict[str, Any]:
        """Make a CQL search request."""
        params: Dict[str, Any] = {
            "cql": cql,
            "start": start,
            "limit": limit
        }
        if expand:
            params["expand"] = expand

        response = await self._make_api_request(
            method="GET",
            endpoint=APIConstants.SEARCH_ENDPOINT,
            service_name="confluence_cql_search",
            params=params
        )

        if not isinstance(response, dict):
            self.logger.error(
                f"Type de réponse inattendu pour la requête CQL: {type(response)}. "
                f"Attendu: dict. Réponse: {str(response)[:200]}"
            )
            raise APIError(f"Type de réponse inattendu pour la requête CQL: {type(response)}")

        return response

    async def _estimate_total_results(self, cql: str) -> int:
        """Estimate the total number of results for a CQL query."""
        try:
            response = await self._make_cql_request(cql=cql, start=0, limit=1)

            if isinstance(response.get('totalSize'), int):
                return response['totalSize']

            if isinstance(response.get('size'), int) and 'totalSize' not in response:
                self.logger.warning(
                    f"Le champ 'totalSize' est manquant dans la réponse CQL. "
                    f"Utilisation de 'size' comme fallback. Clés: {response.keys()}"
                )
                return response['size']

            self.logger.warning(
                f"Champs 'totalSize' ou 'size' non trouvés dans la réponse CQL. "
                f"Réponse: {str(response)[:200]}"
            )
            return 0

        except Exception as e:
            self.logger.warning(
                f"Impossible d'estimer le nombre total de résultats pour CQL "
                f"'{cql[:50]}...': {e}", exc_info=False
            )
            return 0

    async def _fetch_page_parallel(
            self,
            cql: str,
            start: int,
            limit: int,
            expand: str
    ) -> List[Dict[str, Any]]:
        """Fetch a single page of results in parallel processing."""
        response_data = await self._make_api_request(
            method="GET",
            endpoint=APIConstants.SEARCH_ENDPOINT,
            service_name="confluence_fetch_page",
            params={
                "cql": cql,
                "start": start,
                "limit": limit,
                "expand": expand
            }
        )

        if isinstance(response_data, dict) and "results" in response_data:
            results = response_data["results"]
            if isinstance(results, list):
                return results
        elif isinstance(response_data, list):
            self.logger.warning(
                "Réponse de _fetch_page_parallel est une liste directe, "
                "attendu un dict avec 'results'."
            )
            return response_data

        self.logger.warning(
            f"Structure de données inattendue de _fetch_page_parallel: {type(response_data)}. "
            f"Attendu: dict avec 'results'. Réponse: {str(response_data)[:200]}"
        )
        return []

    def _build_expand_string(self, criteria: SearchCriteria) -> str:
        """Build the expand string based on search criteria."""
        expand_fields = APIConstants.STANDARD_EXPAND_FIELDS.copy()

        if criteria.include_attachments:
            expand_fields.extend(APIConstants.ATTACHMENT_EXPAND_FIELDS)

        return ','.join(list(set(expand_fields)))

    def _calculate_effective_limits(
            self,
            total_results_estimation: int,
            max_results: int
    ) -> Tuple[int, int]:
        """Calculate effective limits for fetching results."""
        effective_max_results = total_results_estimation

        if max_results > 0:
            effective_max_results = min(total_results_estimation, max_results)
        elif max_results <= 0 and total_results_estimation > 0:
            effective_max_results = total_results_estimation

        page_size = min(
            getattr(self.config, 'default_page_size', APIConstants.DEFAULT_PAGE_SIZE),
            APIConstants.MAX_PAGE_SIZE
        )

        if max_results > 0:
            page_size = min(page_size, max_results)

        return effective_max_results, page_size

    async def _search_content_parallel(self, criteria: SearchCriteria) -> List[ContentItem]:
        """Search content using parallel requests."""
        cql = criteria.to_cql()
        expand = self._build_expand_string(criteria)

        self.logger.debug(f"Using expansion string: {expand}")

        # Estimate total results
        total_results_estimation = await self._estimate_total_results(cql)
        self.logger.info(
            f"Estimation du nombre total de résultats pour CQL "
            f"'{cql[:100]}...': {total_results_estimation}"
        )

        # Calculate effective limits
        effective_max_results, page_size = self._calculate_effective_limits(
            total_results_estimation, criteria.max_results
        )

        if effective_max_results <= 0:
            self.logger.info(f"Aucun résultat à récupérer pour CQL: {cql}")
            return []

        # Calculate pages for parallel fetching
        pages = PaginationCalculator.calculate_pages(
            total_results_estimation, criteria.max_results, page_size
        )

        if not pages:
            self.logger.info(f"Aucune page à récupérer pour CQL: {cql}")
            return []

        self.logger.info(
            f"Récupération de {len(pages)} pages en parallèle. "
            f"Taille par page ~{page_size}. "
            f"Total visé: {effective_max_results} "
            f"(sur estimation de {total_results_estimation})."
        )

        return await self._fetch_all_pages_parallel(cql, expand, pages, criteria.max_results)

    async def _fetch_all_pages_parallel(
            self,
            cql: str,
            expand: str,
            pages: List[Tuple[int, int]],
            max_results: int
    ) -> List[ContentItem]:
        """Fetch all pages in parallel and convert to ContentItems."""
        max_concurrent = getattr(
            self.config, 'max_concurrent_api_calls',
            APIConstants.MAX_CONCURRENT_CALLS
        )
        semaphore = asyncio.Semaphore(max_concurrent)

        async def fetch_with_semaphore(cql_query, start_offset, limit_val, expand_val):
            async with semaphore:
                return await self._fetch_page_parallel(
                    cql_query, start_offset, limit_val, expand_val
                )

        tasks = [
            fetch_with_semaphore(cql, start, limit, expand)
            for start, limit in pages
        ]

        results_pages_or_exceptions = await asyncio.gather(
            *tasks, return_exceptions=True
        )

        return self._process_parallel_results(
            results_pages_or_exceptions, pages, max_results
        )

    def _process_parallel_results(
            self,
            results_pages_or_exceptions: List[Union[List[Dict[str, Any]], Exception]],
            pages: List[Tuple[int, int]],
            max_results: int
    ) -> List[ContentItem]:
        """Process results from parallel page fetching."""
        content_items: List[ContentItem] = []
        count = 0

        for i, page_data in enumerate(results_pages_or_exceptions):
            if isinstance(page_data, Exception):
                self.logger.error(
                    f"Erreur lors de la récupération de la page {i} "
                    f"(start={pages[i][0]}, limit={pages[i][1]}): {page_data}"
                )
                continue

            if not isinstance(page_data, list):
                self.logger.warning(
                    f"page_data pour la page {i} n'est pas une liste: {type(page_data)}"
                )
                continue

            for result_json in page_data:
                if max_results > 0 and count >= max_results:
                    break

                if not isinstance(result_json, dict):
                    self.logger.warning(
                        f"Item dans page_data n'est pas un dict: {type(result_json)}"
                    )
                    continue

                try:
                    content_item = ContentItem.from_json(result_json)
                    content_items.append(content_item)
                    count += 1
                except Exception as e:
                    self.logger.warning(
                        f"Erreur lors de la conversion d'un résultat en ContentItem: {e}. "
                        f"Résultat: {str(result_json)[:200]}", exc_info=True
                    )

            if max_results > 0 and count >= max_results:
                break

        # Ensure we don't exceed max_results
        if max_results > 0 and len(content_items) > max_results:
            content_items = content_items[:max_results]

        self.logger.info(f"{len(content_items)} ContentItems récupérés.")
        return content_items

    async def search_content(self, criteria: SearchCriteria) -> List[ContentItem]:
        """Recherche des contenus Confluence selon les critères spécifiés."""
        self.logger.info(f"Début de la recherche de contenu avec les critères: {criteria}")
        return await self._search_content_parallel(criteria)

    async def get_content(self, content_id: str) -> ContentItem:
        """Récupère les détails complets d'un élément de contenu Confluence par son ID."""
        self.logger.info(f"Récupération du contenu ID: {content_id}")

        expand_fields = APIConstants.STANDARD_EXPAND_FIELDS + ['children.page']
        expand = ','.join(list(set(expand_fields)))
        endpoint = f"{APIConstants.CONTENT_ENDPOINT}/{content_id}"
        params = {"expand": expand}

        try:
            content_json: Dict[str, Any] = await self._make_api_request(
                method="GET",
                endpoint=endpoint,
                service_name="confluence_get_content",
                params=params
            )

            if not isinstance(content_json, dict):
                self.logger.error(
                    f"Type de réponse inattendu pour get_content ID {content_id}: "
                    f"{type(content_json)}. Attendu: dict."
                )
                raise APIError(f"Type de réponse inattendu: {type(content_json)}")

            return ContentItem.from_json(content_json)

        except ContentNotFoundError:
            self.logger.warning(f"Contenu ID {content_id} non trouvé.")
            raise
        except Exception as e:
            self.logger.error(
                f"Erreur lors de la récupération du contenu ID {content_id}: {e}",
                exc_info=True
            )
            if isinstance(e, (APIError, ContentNotFoundError)):
                raise
            raise APIError(
                f"Erreur lors de la récupération du contenu ID {content_id}: {str(e)}"
            ) from e

    async def get_attachments(self, content_id: str) -> List[AttachmentDetail]:
        """Récupère la liste des pièces jointes pour un élément de contenu Confluence."""
        self.logger.info(f"Récupération des pièces jointes pour le contenu ID: {content_id}")

        attachments_list: List[AttachmentDetail] = []
        start = 0
        limit = getattr(
            self.config, 'attachment_page_size',
            APIConstants.ATTACHMENT_PAGE_SIZE
        )
        expand = ','.join(APIConstants.VERSION_EXPAND_FIELDS)
        endpoint = f"{APIConstants.CONTENT_ENDPOINT}/{content_id}/child/attachment"

        page_count = 0
        max_attachment_pages = getattr(self.config, 'max_attachment_pages', APIConstants.MAX_ATTACHMENT_PAGES)

        while True:
            page_count += 1
            if page_count > max_attachment_pages:
                self.logger.warning(f"Limite de pagination ({max_attachment_pages} pages) atteinte pour les pièces jointes du contenu ID {content_id}.")
                break

            params = {"start": start, "limit": limit, "expand": expand}
            try:
                response_json: Dict[str, Any] = await self._make_api_request(
                    method="GET",
                    endpoint=endpoint,
                    service_name="confluence_get_attachments",
                    params=params
                )

                if not isinstance(response_json, dict) or "results" not in response_json or not isinstance(response_json["results"], list):
                    self.logger.error(f"Structure de réponse inattendue pour get_attachments (ID {content_id}, start {start}): {type(response_json)}. Attendu: dict avec 'results' list.")
                    if isinstance(response_json, dict) and response_json.get("results") == [] and response_json.get("size", 0) == 0:
                        break
                    raise APIError(f"Structure de réponse inattendue pour get_attachments: {type(response_json)}")

                attachment_results = response_json.get("results", [])
                for att_json in attachment_results:
                    if not isinstance(att_json, dict):
                        self.logger.warning(f"Item d'attachement non-dict pour content ID {content_id}: {type(att_json)}")
                        continue
                    try:
                        attachment = AttachmentDetail.from_json(att_json, content_id=content_id)
                        attachments_list.append(attachment)
                    except Exception as e:
                        self.logger.warning(f"Erreur lors de la conversion d'un résultat d'attachement en AttachmentDetail "
                                            f"pour content ID {content_id}: {e}. Attachement JSON: {str(att_json)[:200]}", exc_info=False)

                current_page_size = response_json.get("size", len(attachment_results))
                if current_page_size < limit or not response_json.get("_links", {}).get("next"):
                    break

                start += current_page_size

            except ContentNotFoundError:
                self.logger.info(f"Aucune ressource de pièce jointe (/child/attachment) trouvée pour le contenu ID {content_id}.")
                break
            except Exception as e:
                self.logger.error(f"Erreur lors de la récupération d'une page de pièces jointes pour {content_id}: {e}", exc_info=True)
                if isinstance(e, (APIError, RateLimitExceededError)):
                    raise
                break

        self.logger.info(f"{len(attachments_list)} pièces jointes récupérées pour le contenu ID: {content_id}")
        return attachments_list

    async def download_attachment(self, attachment_detail: AttachmentDetail) -> bytes:
        """
        Télécharge le contenu d'une pièce jointe.
        """
        if not attachment_detail.download_url:
            self.logger.error(f"Impossible de télécharger la pièce jointe ID={attachment_detail.id}, "
                              f"Nom='{attachment_detail.file_name}': "
                              "l'attribut 'download_url' est manquant ou vide dans AttachmentDetail.")
            raise ValueError(f"AttachmentDetail (ID: {attachment_detail.id}, Nom: {attachment_detail.file_name}) "
                             "manque de 'download_url'.")

        download_url_str = str(attachment_detail.download_url)

        self.logger.info(f"Tentative de téléchargement de la pièce jointe: ID={attachment_detail.id}, "
                         f"Nom='{attachment_detail.file_name}', Link='{download_url_str}'")

        try:
            attachment_data: bytes = await self._make_api_request(
                method="GET",
                endpoint=download_url_str,
                service_name="confluence_attachment_download",
                download_mode=True,
                retry_decorator=self.download_retry
            )
            self.logger.info(f"Pièce jointe ID={attachment_detail.id} ('{attachment_detail.file_name}') "
                             f"téléchargée avec succès ({len(attachment_data)} octets).")
            return attachment_data
        except ContentNotFoundError:
            self.logger.error(f"Pièce jointe non trouvée (404): ID={attachment_detail.id} "
                              f"('{attachment_detail.file_name}') au lien '{download_url_str}'")
            raise
        except APIError as e:
            self.logger.error(f"Erreur API lors du téléchargement de la pièce jointe ID={attachment_detail.id} "
                              f"('{attachment_detail.file_name}'): {e}")
            raise
        except Exception as e:
            sanitized_error = SecurityUtils.sanitize_error_message(str(e))
            self.logger.error(f"Erreur inattendue lors du téléchargement de la pièce jointe ID={attachment_detail.id} "
                              f"('{attachment_detail.file_name}'): {sanitized_error}", exc_info=True)
            raise APIError(f"Erreur inattendue de téléchargement: {sanitized_error}") from e

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
HTTP request and response handling for the Confluence  client.
"""

import logging
import aiohttp
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .constants import APIConstants
from .security import SecurityUtils
from .exceptions import (
    AuthenticationError, APIError, ContentNotFoundError, RateLimitExceededError
)


@dataclass
class RequestContext:
    """Context information for API requests."""
    method: str
    endpoint: str
    service_name: str
    download_mode: bool = False
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None


class ResponseProcessor:
    """Handles response processing and validation."""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    async def process_response(
            self,
            response: aiohttp.ClientResponse,
            context: RequestContext
    ) -> Any:
        """Process HTTP response based on context."""
        self.logger.debug(
            f"Response status from {context.endpoint}: {response.status}, "
            f"Content-Type: {response.headers.get('Content-Type')}"
        )

        if response.status == 200:
            return await self._handle_success_response(response, context)
        else:
            await self._handle_error_response(response, context)

    async def _handle_success_response(
            self,
            response: aiohttp.ClientResponse,
            context: RequestContext
    ) -> Any:
        """Handle successful HTTP responses."""
        if context.download_mode:
            return await response.read()

        content_type = response.headers.get('Content-Type', '').lower()
        if APIConstants.JSON_CONTENT_TYPE in content_type:
            return await response.json()
        else:
            self.logger.debug(
                f"Response from {context.endpoint} is not JSON "
                f"(Content-Type: {content_type}). Reading as text."
            )
            return await response.text()

    async def _handle_error_response(
            self,
            response: aiohttp.ClientResponse,
            context: RequestContext
    ) -> None:
        """Handle HTTP error responses."""
        error_text = await response.text()
        sanitized_error = SecurityUtils.sanitize_error_message(error_text)

        if response.status == 401:
            self.logger.error(f"Erreur d'authentification (401): {sanitized_error}")
            raise AuthenticationError(f"Erreur d'authentification: {sanitized_error}")
        elif response.status == 404:
            self.logger.warning(f"Ressource non trouvée (404): {context.endpoint}")
            raise ContentNotFoundError(f"Ressource non trouvée: {context.endpoint}")
        elif response.status == 429:
            retry_after = self._parse_retry_after(response.headers.get('Retry-After'))
            self.logger.warning(f"Limite de taux (429) dépassée. Retry-After: {retry_after}s")
            raise RateLimitExceededError(
                "Limite de taux d'appels API dépassée",
                retry_after=retry_after
            )
        else:
            self.logger.error(f"Erreur API {response.status}: {sanitized_error}")
            raise APIError(
                f"Erreur API: {response.status} - {sanitized_error}",
                status_code=response.status
            )

    def _parse_retry_after(self, retry_after_header: Optional[str]) -> int:
        """Parse Retry-After header value."""
        if not retry_after_header:
            return APIConstants.DEFAULT_RETRY_AFTER

        try:
            return int(retry_after_header)
        except ValueError:
            self.logger.warning(
                f"Invalid Retry-After header: {retry_after_header}. "
                f"Defaulting to {APIConstants.DEFAULT_RETRY_AFTER}s."
            )
            return APIConstants.DEFAULT_RETRY_AFTER

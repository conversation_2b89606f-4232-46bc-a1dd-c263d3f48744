#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Utilitaires de journalisation structurée pour le système RAG Confluence.
"""

import json
import logging
import uuid
import threading
import functools
import asyncio

from typing import Dict, Any, Optional, Callable, TypeVar, Awaitable, cast
from datetime import datetime
import traceback
import sys
from contextvars import ContextVar
from types import TracebackType # For type hinting

# Type variables pour les décorateurs
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Awaitable[Any]])


# --- Standalone Sanitization Logic ---
def sanitize_string(text: str) -> str:
    """
    Nettoie une chaîne de caractères en supprimant les informations sensibles.
    Utilise la méthode centralisée de SecurityUtils pour assurer la cohérence.

    Args:
        text: La chaîne de caractères originale.

    Returns:
        La chaîne de caractères nettoyée.
    """
    # Import here to avoid circular imports
    from .security import SecurityUtils
    return SecurityUtils.sanitize_error_message(text)
# --- End Standalone Sanitization Logic ---


class CorrelationContext:
    """
    Contexte pour stocker et récupérer les identifiants de corrélation.
    Utilise un contexte local au thread et un contexte de tâche asyncio.
    """
    _thread_local = threading.local()
    _task_context_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)

    @classmethod
    def get_correlation_id(cls) -> Optional[str]:
        """
        Récupère l'identifiant de corrélation actuel.
        Vérifie d'abord le contexte asyncio, puis le contexte de thread.
        """
        # Vérifier d'abord le contexte asyncio
        correlation_id = cls._task_context_var.get()
        if correlation_id:
            return correlation_id

        # Sinon, vérifier le contexte de thread
        return getattr(cls._thread_local, 'correlation_id', None)

    @classmethod
    def set_correlation_id(cls, correlation_id: str) -> None:
        """
        Définit l'identifiant de corrélation dans les deux contextes.
        """
        # Définir dans le contexte de thread
        cls._thread_local.correlation_id = correlation_id

        # Définir dans le contexte asyncio si nous sommes dans une boucle asyncio
        try:
            cls._task_context_var.set(correlation_id)
        except RuntimeError:
            # Pas dans une boucle asyncio, ignorer
            pass

    @classmethod
    def generate_correlation_id(cls) -> str:
        """
        Génère un nouvel identifiant de corrélation unique et le définit comme identifiant actuel.
        """
        correlation_id = str(uuid.uuid4())
        cls.set_correlation_id(correlation_id)
        return correlation_id

    @classmethod
    def clear_correlation_id(cls) -> None:
        """
        Efface l'identifiant de corrélation des deux contextes.
        """
        # Effacer du contexte de thread
        if hasattr(cls._thread_local, 'correlation_id'):
            delattr(cls._thread_local, 'correlation_id')

        # Essayer d'effacer du contexte asyncio
        try:
            cls._task_context_var.set(None)
        except RuntimeError:
            # Pas dans une boucle asyncio, ignorer
            pass


class SecurityFilter(logging.Filter):
    """
    Filtre de logging qui supprime les informations sensibles des messages de log.
    """

    def filter(self, record: logging.LogRecord) -> bool:
        """
        Nettoie le message de log et ajoute l'identifiant de corrélation.
        """
        # Nettoyer le message de log
        if hasattr(record, 'msg') and record.msg:
            record.msg = sanitize_string(str(record.msg))

        # Nettoyer les arguments du message
        if hasattr(record, 'args') and record.args:
            sanitized_args = []
            for arg in record.args:
                if isinstance(arg, str):
                    sanitized_args.append(sanitize_string(arg))
                else:
                    sanitized_args.append(arg)
            record.args = tuple(sanitized_args)

        # Nettoyer les attributs supplémentaires (pour le logging structuré)
        for key, value in record.__dict__.items():
            if not key.startswith('_') and key not in ['name', 'msg', 'args', 'levelname', 'levelno',
                                                       'pathname', 'filename', 'module', 'lineno',
                                                       'funcName', 'created', 'msecs', 'relativeCreated',
                                                       'thread', 'threadName', 'processName', 'process',
                                                       'exc_info', 'exc_text', 'stack_info']:
                if isinstance(value, str):
                    setattr(record, key, sanitize_string(value))
                elif isinstance(value, dict):
                    # Nettoyer les dictionnaires récursivement
                    sanitized_dict = {}
                    for k, v in value.items():
                        if isinstance(v, str):
                            sanitized_dict[k] = sanitize_string(v)
                        else:
                            sanitized_dict[k] = v
                    setattr(record, key, sanitized_dict)

        # Toujours autoriser l'enregistrement
        return True


class CorrelationIdFilter(logging.Filter):
    """
    Filtre de logging qui ajoute l'identifiant de corrélation actuel aux enregistrements de log.
    """

    def filter(self, record: logging.LogRecord) -> bool:
        """
        Ajoute l'identifiant de corrélation à l'enregistrement de log.
        """
        # Récupérer l'identifiant de corrélation actuel
        correlation_id = CorrelationContext.get_correlation_id()

        # Ajouter l'identifiant à l'enregistrement
        record.correlation_id = correlation_id or 'no-correlation-id'

        # Toujours autoriser l'enregistrement
        return True


class StructuredLogFormatter(logging.Formatter):
    """
    Formateur de logs qui produit des logs structurés au format JSON.
    """

    def __init__(self, include_traceback: bool = True):
        """
        Initialise le formateur.

        Args:
            include_traceback: Si True, inclut la trace d'appel pour les erreurs
        """
        super().__init__()
        self.include_traceback = include_traceback

    def format(self, record: logging.LogRecord) -> str:
        """
        Formate l'enregistrement de log en JSON structuré.
        """
        # Créer la structure de base du log
        log_data: Dict[str, Any] = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "correlation_id": getattr(record, 'correlation_id', 'no-correlation-id'),
            "message": sanitize_string(record.getMessage()), # Sanitize the final formatted message
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread_id": record.thread,
            "thread_name": record.threadName,
            "process_id": record.process
        }

        # Ajouter les informations d'exception si présentes
        if record.exc_info and self.include_traceback:
            # record.exc_info is a tuple (type, value, traceback)
            exc_type: Optional[type[BaseException]] = record.exc_info[0]
            exc_value: Optional[BaseException] = record.exc_info[1]
            exc_tb: Optional[TracebackType] = record.exc_info[2]

            if exc_type and exc_value and exc_tb: # Ensure all parts are present
                exception_message = str(exc_value)

                # Call format_exception with explicit arguments
                formatted_exception_list = traceback.format_exception(exc_type, exc_value, exc_tb)
                exception_traceback = "".join(formatted_exception_list)

                log_data["exception"] = {
                    "type": exc_type.__name__,
                    "message": sanitize_string(exception_message),
                    "traceback": sanitize_string(exception_traceback)
                }
            elif exc_value: # Fallback if only value is present (less common)
                log_data["exception"] = {
                    "type": exc_value.__class__.__name__,
                    "message": sanitize_string(str(exc_value)),
                    "traceback": "Traceback information incomplete"
                }


        # Ajouter les attributs supplémentaires
        for key, value in record.__dict__.items():
            if key not in log_data and not key.startswith('_') and key != 'exc_info':
                try:
                    # Sanitize if it's a string before trying to dump
                    if isinstance(value, str):
                        sanitized_value = sanitize_string(value)
                    else:
                        sanitized_value = value

                    json.dumps({key: sanitized_value})  # Tester si sérialisable
                    log_data[key] = sanitized_value
                except (TypeError, OverflowError):
                    log_data[key] = sanitize_string(str(value)) # Sanitize the string representation

        # Convertir en JSON
        return json.dumps(log_data, ensure_ascii=False)


def with_correlation_id(func: Callable[..., T]) -> Callable[..., T]:
    """
    Décorateur qui génère un nouvel identifiant de corrélation pour la fonction.
    Fonctionne avec les fonctions synchrones et asynchrones.
    """
    @functools.wraps(func)
    def sync_wrapper(*args: Any, **kwargs: Any) -> T:
        # Générer un nouvel identifiant de corrélation
        CorrelationContext.generate_correlation_id()
        try:
            return func(*args, **kwargs)
        finally:
            CorrelationContext.clear_correlation_id()

    @functools.wraps(func)
    async def async_wrapper(*args: Any, **kwargs: Any) -> T: # Type T for return
        # Générer un nouvel identifiant de corrélation
        CorrelationContext.generate_correlation_id()
        try:
            # Since func could be sync or async, and T is the return type of func
            # we need to cast the func if it's an async function.
            # However, the iscoroutinefunction check below handles this.
            result = func(*args, **kwargs)
            if asyncio.iscoroutine(result):
                return await result # type: ignore # mypy might complain if func is sync
            return result # type: ignore # mypy might complain if func is async
        finally:
            CorrelationContext.clear_correlation_id()

    if asyncio.iscoroutinefunction(func):
        # functools.wraps needs help with async functions for type hints sometimes
        return cast(Callable[..., T], async_wrapper)
    else:
        return cast(Callable[..., T], sync_wrapper)


def propagate_correlation_id(func: F) -> F:
    """
    Décorateur qui propage l'identifiant de corrélation actuel à la fonction.
    Utilisé pour les fonctions appelées à partir d'une fonction avec un identifiant de corrélation.
    Ce décorateur est conçu pour les fonctions asynchrones.
    """
    @functools.wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> Any: # Return type is Any as F is generic
        # Récupérer l'identifiant de corrélation actuel
        current_correlation_id = CorrelationContext.get_correlation_id()
        outer_task_id = None

        # Store current task's correlation_id if any
        # This is to ensure we restore the correct context if this function
        # is called from another asyncio task that *already* has a correlation_id.
        try:
            outer_task_id = CorrelationContext._task_context_var.get()
        except RuntimeError: # Not in an asyncio task or context var not set.
            pass

        if current_correlation_id:
            CorrelationContext.set_correlation_id(current_correlation_id)

        try:
            return await func(*args, **kwargs)
        finally:
            # Restore the correlation_id that was active in this task before this function call.
            # If this function was the one that set the initial ID based on thread_local,
            # clearing it here might be too aggressive if other async tasks spawned by it
            # are still running and expect it.
            # A safer approach if current_correlation_id was from thread_local is to simply
            # restore the 'outer_task_id' to the context var.
            if outer_task_id is not None:
                CorrelationContext._task_context_var.set(outer_task_id)
            elif current_correlation_id: # If we set one based on thread_local and there was no prior task_id
                # If no outer_task_id, and we had a current_correlation_id (likely from thread),
                # we might want to clear it from the task context if this is the "boundary"
                # or leave it if other sibling tasks under the same initial correlation might need it.
                # For now, let's assume if it was propagated, it stays for the task's lifetime
                # unless explicitly cleared by with_correlation_id's finally block.
                # The set_correlation_id above already put it in the task_context_var.
                pass
            # No explicit clear here, as the with_correlation_id is responsible for the lifecycle.
            # This decorator *propagates* an existing ID.

    return cast(F, wrapper)


def propagate_correlation_id_sync(func: F) -> F:
    """
    Décorateur qui propage l'identifiant de corrélation actuel à la fonction synchrone.
    Version synchrone de propagate_correlation_id.
    """
    @functools.wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> Any:
        # Récupérer l'identifiant de corrélation actuel
        current_correlation_id = CorrelationContext.get_correlation_id()

        if current_correlation_id:
            # Pour les fonctions synchrones, on utilise le thread-local storage
            # L'ID est déjà disponible via get_correlation_id()
            pass

        return func(*args, **kwargs)

    return cast(F, wrapper)


class LoggingConfig:
    """Configuration simple pour les tests."""
    def __init__(self, level: str = "INFO", enable_console: bool = True, enable_file: bool = False):
        self.level = level
        self.enable_console = enable_console
        self.enable_file = enable_file


def setup_logging(config: Optional[LoggingConfig] = None) -> None:
    """
    Configure le système de logging pour les tests.

    Args:
        config: Configuration de logging (optionnelle)
    """
    if config is None:
        config = LoggingConfig()

    # Configuration de base
    logging.basicConfig(
        level=getattr(logging, config.level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


# --- Example Usage (Optional) ---
if __name__ == '__main__':
    # Configure logging
    logger = logging.getLogger("my_app")
    logger.setLevel(logging.DEBUG)

    # Create a handler (e.g., StreamHandler to output to console)
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)

    # Add filters and formatter
    handler.addFilter(CorrelationIdFilter()) # Adds correlation_id to record
    handler.addFilter(SecurityFilter())      # Sanitizes record fields
    formatter = StructuredLogFormatter(include_traceback=True)
    handler.setFormatter(formatter)

    logger.addHandler(handler)

    # Test logging
    logger.info("This is a normal log message.")
    CorrelationContext.generate_correlation_id()
    logger.info("This log message has a correlation ID.", extra={"user_id": 123, "action": "login"})
    logger.warning("A warning with API key: sk-abcdef1234567890abcdef and password=secretpassword", extra={"sensitive_data": "Authorization: Bearer my_long_bearer_token_here"})

    try:
        x = 1 / 0
    except ZeroDivisionError:
        logger.error("An error occurred", exc_info=True)

    CorrelationContext.clear_correlation_id()
    logger.info("Log after clearing correlation ID.")

    @with_correlation_id
    def my_sync_function(name: str):
        logger.info(f"Hello from sync function, {name}!")
        another_sync_function()

    def another_sync_function():
        logger.info("Log from another_sync_function, using propagated ID.")

    @with_correlation_id
    async def my_async_function(name: str):
        logger.info(f"Hello from async function, {name}!")
        await another_async_function()
        logger.info("Back in my_async_function")

    @propagate_correlation_id # For async functions called by other async functions
    async def another_async_function():
        logger.info("Log from another_async_function, using propagated ID.")
        await asyncio.sleep(0.01) # Simulate async work
        logger.info("Log from another_async_function after sleep, ID should persist.")


    my_sync_function("Alice")

    async def main_async():
        await my_async_function("Bob")
        # Test propagation without explicit with_correlation_id (e.g., in a web request handler)
        CorrelationContext.generate_correlation_id()
        try:
            logger.info("Manually set correlation ID for this block.")
            await another_async_function() # Should pick up the manual ID
        finally:
            CorrelationContext.clear_correlation_id()

    asyncio.run(main_async())

    # Test nested with_correlation_id (should generate a new one)
    @with_correlation_id
    def outer_function_with_id():
        logger.info("Outer function has its own ID.")
        inner_function_with_id()

    @with_correlation_id
    def inner_function_with_id():
        logger.info("Inner function gets a NEW ID, not propagated from outer.")

    outer_function_with_id()

    # Test contextvar behavior with asyncio tasks
    async def task_worker(task_name: str):
        logger.info(f"Task {task_name} starting, Correlation ID: {CorrelationContext.get_correlation_id()}")
        await asyncio.sleep(0.1)
        logger.info(f"Task {task_name} finishing, Correlation ID: {CorrelationContext.get_correlation_id()}")

    @with_correlation_id
    async def run_concurrent_tasks():
        logger.info("Main task for concurrency test")
        await asyncio.gather(
            task_worker("A"),
            task_worker("B")
        )
        logger.info(f"Main task for concurrency test - after gather. ID: {CorrelationContext.get_correlation_id()}")


    asyncio.run(run_concurrent_tasks())
    logger.info("Finished all tests.")
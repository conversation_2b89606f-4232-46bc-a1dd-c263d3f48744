# Guide de migration vers l'orchestrateur Confluence synchrone

## Vue d'ensemble

Ce guide vous aide à migrer du code utilisant l'ancien orchestrateur asyncio vers le nouvel orchestrateur synchrone. La migration simplifie le code, améliore les performances et unifie l'architecture.

## Changements principaux

### 1. Imports

**Avant (asyncio) :**
```python
from kbotloadscheduler.loader.confluence import (
    ConfluenceClient,
    ContentRetriever,
    AttachmentProcessor
)
```

**Après (synchrone) :**
```python
from kbotloadscheduler.loader.confluence import (
    SyncConfluenceClient,
    SyncContentRetriever,
    SyncAttachmentProcessor,
    SyncOrchestrator
)
```

### 2. Création de l'orchestrateur

**Avant (asyncio) :**
```python
import asyncio

async def process_confluence():
    async with Orchestrator(config, criteria) as orchestrator:
        result = await orchestrator.run()
    return result

# Utilisation
loop = asyncio.new_event_loop()
result = loop.run_until_complete(process_confluence())
loop.close()
```

**Après (synchrone) :**
```python
def process_confluence():
    orchestrator = SyncOrchestrator(config, criteria)
    result = orchestrator.run()
    return result

# Utilisation directe
result = process_confluence()
```

### 3. Configuration

**Aucun changement requis** - Toutes les configurations existantes sont compatibles :
- `ConfluenceConfig`
- `SearchCriteria`
- `StorageConfig`
- `ProcessingConfig`

### 4. Gestion des erreurs

**Avant (asyncio) :**
```python
try:
    async with Orchestrator(config, criteria) as orchestrator:
        result = await orchestrator.run()
except ConfluenceRAGException as e:
    logger.error(f"Confluence error: {e}")
```

**Après (synchrone) :**
```python
try:
    orchestrator = SyncOrchestrator(config, criteria)
    result = orchestrator.run()
except ConfluenceRAGException as e:
    logger.error(f"Confluence error: {e}")
```

## Exemples de migration

### Exemple 1 : Script simple

**Avant :**
```python
import asyncio
from kbotloadscheduler.loader.confluence import Orchestrator

async def sync_confluence():
    config = ConfluenceConfig(url="...", pat_token="...")
    criteria = SearchCriteria(spaces=["SPACE1"])
    
    async with Orchestrator(config, criteria) as orchestrator:
        return await orchestrator.run()

if __name__ == "__main__":
    result = asyncio.run(sync_confluence())
    print(f"Synced {result['total_content_items']} items")
```

**Après :**
```python
from kbotloadscheduler.loader.confluence import SyncOrchestrator

def sync_confluence():
    config = ConfluenceConfig(url="...", pat_token="...")
    criteria = SearchCriteria(spaces=["SPACE1"])
    
    orchestrator = SyncOrchestrator(config, criteria)
    return orchestrator.run()

if __name__ == "__main__":
    result = sync_confluence()
    print(f"Synced {result['total_content_items']} items")
```

### Exemple 2 : Intégration dans une classe

**Avant :**
```python
class ConfluenceService:
    async def sync_spaces(self, spaces: List[str]):
        config = self._create_config()
        criteria = SearchCriteria(spaces=spaces)
        
        async with Orchestrator(config, criteria) as orchestrator:
            result = await orchestrator.run()
            
        return self._process_result(result)
    
    def run_sync(self, spaces: List[str]):
        loop = asyncio.new_event_loop()
        try:
            return loop.run_until_complete(self.sync_spaces(spaces))
        finally:
            loop.close()
```

**Après :**
```python
class ConfluenceService:
    def sync_spaces(self, spaces: List[str]):
        config = self._create_config()
        criteria = SearchCriteria(spaces=spaces)
        
        orchestrator = SyncOrchestrator(config, criteria)
        result = orchestrator.run()
            
        return self._process_result(result)
    
    # Plus besoin de run_sync - utilisez directement sync_spaces
```

### Exemple 3 : Traitement par lots

**Avant :**
```python
async def process_multiple_spaces(space_configs):
    results = []
    for config, criteria in space_configs:
        async with Orchestrator(config, criteria) as orchestrator:
            result = await orchestrator.run()
            results.append(result)
    return results
```

**Après :**
```python
def process_multiple_spaces(space_configs):
    results = []
    for config, criteria in space_configs:
        orchestrator = SyncOrchestrator(config, criteria)
        result = orchestrator.run()
        results.append(result)
    return results
```

## Configuration du parallélisme

L'orchestrateur synchrone utilise `ThreadPoolExecutor` pour le parallélisme :

```python
from kbotloadscheduler.loader.confluence import ProcessingConfig, ThreadPoolConfig

# Configuration haute performance
processing_config = ProcessingConfig(
    max_thread_workers=8,
    max_parallel_downloads=10,
    thread_pool_config=ThreadPoolConfig(
        io_thread_workers=12,
        document_processing_workers=6,
        api_thread_workers=8
    )
)

orchestrator = SyncOrchestrator(
    config, 
    criteria, 
    processing_config=processing_config
)
```

## Avantages de la migration

### 1. Simplicité du code
- ✅ Plus de `async`/`await`
- ✅ Plus de gestion de boucles d'événements
- ✅ Code plus linéaire et prévisible

### 2. Performance
- ✅ Parallélisme contrôlé avec ThreadPoolExecutor
- ✅ Pas d'overhead asyncio
- ✅ Utilisation mémoire optimisée

### 3. Debugging
- ✅ Stack traces plus claires
- ✅ Debugging plus facile
- ✅ Profiling simplifié

### 4. Intégration
- ✅ Compatible avec les systèmes synchrones
- ✅ Pas de conflit avec d'autres boucles d'événements
- ✅ Intégration transparente dans kbot-load-scheduler

## Checklist de migration

### Étape 1 : Mise à jour des imports
- [ ] Remplacer `ConfluenceClient` par `SyncConfluenceClient`
- [ ] Remplacer `ContentRetriever` par `SyncContentRetriever`
- [ ] Remplacer `AttachmentProcessor` par `SyncAttachmentProcessor`
- [ ] Importer `SyncOrchestrator`

### Étape 2 : Suppression d'asyncio
- [ ] Supprimer les imports `asyncio`
- [ ] Supprimer les mots-clés `async`/`await`
- [ ] Supprimer `asyncio.run()` et `loop.run_until_complete()`
- [ ] Supprimer les context managers `async with`

### Étape 3 : Mise à jour du code
- [ ] Remplacer `Orchestrator` par `SyncOrchestrator`
- [ ] Appeler directement `.run()` sans `await`
- [ ] Simplifier la gestion d'erreurs

### Étape 4 : Tests
- [ ] Exécuter les tests existants
- [ ] Vérifier les performances
- [ ] Valider les résultats

### Étape 5 : Optimisation (optionnel)
- [ ] Ajuster la configuration des pools de threads
- [ ] Optimiser le parallélisme selon vos besoins
- [ ] Monitorer les performances

## Dépannage

### Problème : Import Error
```
ImportError: cannot import name 'Orchestrator'
```
**Solution :** Utilisez `SyncOrchestrator` à la place.

### Problème : Syntax Error
```
SyntaxError: 'await' outside async function
```
**Solution :** Supprimez les mots-clés `async`/`await`.

### Problème : Performance dégradée
**Solution :** Ajustez la configuration des pools de threads :
```python
thread_pool_config = ThreadPoolConfig(
    io_thread_workers=8,
    document_processing_workers=4,
    api_thread_workers=6
)
```

## Support

Pour toute question sur la migration :
1. Consultez la documentation dans `docs/`
2. Examinez les tests dans `tests/test_sync_*`
3. Référez-vous aux exemples dans ce guide

La migration vers l'orchestrateur synchrone simplifie votre code tout en maintenant ou améliorant les performances !

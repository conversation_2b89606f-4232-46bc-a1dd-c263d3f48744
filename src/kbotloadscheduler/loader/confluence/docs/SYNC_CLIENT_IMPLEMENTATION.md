# Client Confluence Synchrone - Implémentation

## Vue d'ensemble

Ce document décrit l'implémentation du client Confluence synchrone (`SyncConfluenceClient`) qui remplace les appels asyncio/aiohttp par des appels synchrones utilisant la bibliothèque `requests`.

## Motivation

L'implémentation originale du `ConfluenceClient` utilise asyncio et aiohttp, créant une inconsistance architecturale avec le reste du codebase kbot-load-scheduler qui est principalement synchrone. Cette inconsistance peut impacter les performances et compliquer la maintenance.

## Architecture

### Modules créés

1. **`sync_auth.py`** - Gestion de l'authentification synchrone
2. **`sync_http_client.py`** - Client HTTP synchrone et traitement des réponses
3. **`sync_client.py`** - Client principal Confluence synchrone

### Composants réutilisés

Les modules suivants sont réutilisés sans modification :
- `config.py` - Configuration
- `constants.py` - Constantes
- `exceptions.py` - Exceptions
- `models.py` - Modèles de données
- `security.py` - Utilitaires de sécurité
- `utils.py` - Utilitaires (avec adaptation pour le retry synchrone)

## Implémentation détaillée

### SyncAuthenticationManager

```python
class SyncAuthenticationManager:
    """Gère l'authentification pour les requêtes synchrones."""
    
    def get_auth_headers_and_auth(self) -> Tuple[Dict[str, str], Optional[Tuple[str, str]]]:
        """Retourne les headers et l'auth tuple pour requests."""
```

**Différences avec la version asyncio :**
- Retourne un tuple `(username, password)` au lieu d'un objet `aiohttp.BasicAuth`
- Compatible avec l'API `requests`

### SyncHTTPClient

```python
class SyncHTTPClient:
    """Client HTTP synchrone utilisant requests."""
    
    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Effectue une requête HTTP synchrone."""
```

**Fonctionnalités :**
- Gestion des timeouts
- Gestion des erreurs de connexion
- Context manager pour la fermeture automatique de la session

### SyncConfluenceClient

```python
class SyncConfluenceClient:
    """Client API Confluence synchrone."""
    
    def search_content(self, criteria: SearchCriteria) -> List[ContentItem]:
        """Recherche synchrone des contenus."""
    
    def get_content(self, content_id: str) -> ContentItem:
        """Récupération synchrone d'un contenu."""
    
    def get_attachments(self, content_id: str) -> List[AttachmentDetail]:
        """Récupération synchrone des pièces jointes."""
    
    def download_attachment(self, attachment_detail: AttachmentDetail) -> bytes:
        """Téléchargement synchrone d'une pièce jointe."""
```

## Gestion des erreurs et retry

### Retry synchrone

Le client implémente un mécanisme de retry synchrone qui remplace les décorateurs asyncio :

```python
def _sync_retry(self, func: Callable, retry_handler: RetryHandler, *args, **kwargs):
    """Méthode de retry synchrone avec backoff exponentiel."""
```

**Fonctionnalités :**
- Backoff exponentiel avec jitter
- Gestion des exceptions spécifiques (rate limiting, erreurs temporaires)
- Logging détaillé des tentatives

### Circuit breaker

Le circuit breaker existant est réutilisé sans modification, étant compatible avec les appels synchrones.

## Utilisation

### Exemple basique

```python
from src.kbotloadscheduler.loader.confluence.sync_client import SyncConfluenceClient
from src.kbotloadscheduler.loader.confluence.config import ConfluenceConfig, SearchCriteria

config = ConfluenceConfig(
    url="https://your-instance.atlassian.net",
    pat_token=SecretStr("your_pat_token"),
    default_space_key="YOUR_SPACE"
)

with SyncConfluenceClient(config) as client:
    criteria = SearchCriteria(spaces=["YOUR_SPACE"], max_results=10)
    results = client.search_content(criteria)
    
    for content in results:
        print(f"Trouvé: {content.title}")
```

### Authentification

Le client supporte les mêmes méthodes d'authentification que la version asyncio :

1. **PAT Token (recommandé) :**
```python
config = ConfluenceConfig(
    url="https://your-instance.atlassian.net",
    pat_token=SecretStr("your_pat_token")
)
```

2. **API Token (legacy) :**
```python
config = ConfluenceConfig(
    url="https://your-instance.atlassian.net",
    username="<EMAIL>",
    api_token=SecretStr("your_api_token")
)
```

## Tests

Des tests unitaires sont fournis dans `tests/test_sync_client.py` pour valider :
- L'initialisation du client
- Les appels API avec mocks
- La gestion d'erreurs
- Les différents types d'authentification

## Migration

### Étapes pour migrer du client asyncio au client synchrone

1. **Remplacer les imports :**
```python
# Avant
from .client import ConfluenceClient

# Après
from .sync_client import SyncConfluenceClient
```

2. **Supprimer les mots-clés async/await :**
```python
# Avant
async def process_confluence():
    async with ConfluenceClient(config) as client:
        results = await client.search_content(criteria)

# Après
def process_confluence():
    with SyncConfluenceClient(config) as client:
        results = client.search_content(criteria)
```

3. **Adapter les boucles d'événements :**
```python
# Avant
import asyncio
asyncio.run(process_confluence())

# Après
process_confluence()
```

## Avantages

1. **Cohérence architecturale** - Alignement avec le reste du codebase synchrone
2. **Simplicité** - Pas de gestion de boucles d'événements
3. **Performance** - Évite le overhead d'asyncio pour des opérations I/O simples
4. **Compatibilité** - Réutilise la configuration et les modèles existants
5. **Maintenance** - Code plus simple à déboguer et maintenir

## Limitations

1. **Pas de parallélisme natif** - Les requêtes sont séquentielles par défaut
2. **Blocking I/O** - Peut bloquer le thread principal lors d'opérations longues

## Recommandations

1. **Utiliser le client synchrone** pour les nouvelles implémentations
2. **Migrer progressivement** les composants existants
3. **Utiliser ThreadPoolExecutor** si du parallélisme est nécessaire
4. **Conserver le client asyncio** pour les cas d'usage nécessitant une haute concurrence

# Tests d'Intégration Confluence

Ce répertoire contient les tests d'intégration pour le module Confluence avec une vraie instance Confluence.

## 🎯 Objectif

Les tests d'intégration permettent de valider le fonctionnement du loader Confluence avec une vraie instance Confluence, contrairement aux tests unitaires qui utilisent des mocks.

## 📁 Structure des Fichiers

```
tests/loader/confluence/
├── test_real_confluence_integration.py  # Tests d'intégration principaux
├── test_performance_integration.py     # Tests de performance
├── run_integration_tests.py            # Script utilitaire pour exécuter les tests
├── demo_integration.py                 # Démonstration guidée
├── .env.example                        # Exemple de configuration
├── pytest.ini                         # Configuration pytest
├── README_INTEGRATION.md               # Ce fichier
└── ...                                 # Autres tests
```

## 🔐 Système de Secrets

Le projet utilise un système de secrets hybride qui fonctionne avec :

1. **Fichiers locaux** pour les tests (`conf/etc/secrets/tests/`)
2. **Google Cloud Secret Manager** pour la production

### Structure des secrets
```
conf/etc/secrets/tests/
├── confluence-credentials/secret                    # Secrets globaux
├── test-perimeter-confluence-credentials/secret     # Secrets par périmètre
├── setup_confluence_secrets.py                     # Script de configuration
└── README_SECRETS.md                               # Documentation détaillée
```

## 🚀 Configuration Rapide

### 1. Prérequis

- Accès à une instance Confluence (Atlassian Cloud ou Server)
- Personal Access Token (PAT) ou credentials API
- Un espace de test avec quelques pages

### 2. Configuration des Secrets

**Option A : Configuration automatique (Recommandée)**
```bash
# Configuration interactive des secrets
python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive

# Ou depuis variables d'environnement
python conf/etc/secrets/tests/setup_confluence_secrets.py --from-env
```

**Option B : Configuration manuelle**
```bash
# Créer les secrets manuellement
mkdir -p conf/etc/secrets/tests/confluence-credentials
echo '{"pat_token": "VOTRE_TOKEN"}' > conf/etc/secrets/tests/confluence-credentials/secret

mkdir -p conf/etc/secrets/tests/test-perimeter-confluence-credentials
echo '{"pat_token": "VOTRE_TOKEN"}' > conf/etc/secrets/tests/test-perimeter-confluence-credentials/secret
```

### 3. Configuration des Variables d'Environnement

1. **Copiez le fichier de configuration :**
   ```bash
   cp tests/loader/confluence/.env.example tests/loader/confluence/.env
   ```

2. **Éditez le fichier .env :**
   ```bash
   # Configuration obligatoire
   CONFLUENCE_URL=https://votre-instance.atlassian.net
   CONFLUENCE_PAT_TOKEN=votre_personal_access_token

   # Configuration optionnelle
   CONFLUENCE_TEST_SPACE=TEST
   CONFLUENCE_TIMEOUT=30
   ```

### 4. Vérification

```bash
# Vérifier les secrets
python conf/etc/secrets/tests/setup_confluence_secrets.py --verify

# Vérifier la configuration complète
cd tests/loader/confluence
python run_integration_tests.py --check-config
```

## 🧪 Exécution des Tests

### Tests Rapides (Recommandé pour débuter)

```bash
# Vérification de la connexion uniquement
python run_integration_tests.py --health-check

# Tests rapides (sans téléchargement de documents)
python run_integration_tests.py --run-fast
```

### Tests Complets

```bash
# Tous les tests d'intégration
python run_integration_tests.py --run-all

# Tests de performance
python run_integration_tests.py --run-performance

# Tous les tests de performance (y compris lents)
python run_integration_tests.py --run-all-performance

# Test spécifique
python run_integration_tests.py --run-specific test_confluence_connection
```

### Démonstration Guidée

```bash
# Script interactif qui vous guide
python demo_integration.py
```

### Avec pytest directement

```bash
# Tous les tests d'intégration
pytest test_real_confluence_integration.py -v -m integration

# Tests rapides uniquement
pytest test_real_confluence_integration.py -v -m "integration and not slow"

# Tests de performance
pytest test_performance_integration.py -v -m performance

# Test spécifique
pytest test_real_confluence_integration.py::TestRealConfluenceIntegration::test_health_check_real -v
```

## 📋 Types de Tests Disponibles

### Tests d'Intégration

| Test | Durée | Description | Marqueurs |
|------|-------|-------------|-----------|
| `test_confluence_connection` | < 1s | Vérification de l'instanciation du loader | `integration` |
| `test_health_check_real` | < 5s | Test de connexion à Confluence | `integration` |
| `test_get_document_list_real` | 5-15s | Récupération de la liste des documents | `integration` |
| `test_get_document_real` | 15-60s | Téléchargement complet d'un document | `integration`, `slow` |

### Tests de Performance

| Test | Durée | Description | Marqueurs |
|------|-------|-------------|-----------|
| `test_document_list_performance` | Variable | Mesure des temps de récupération | `performance` |
| `test_connection_performance` | < 10s | Performance d'initialisation | `performance` |
| `test_large_result_set_performance` | 30-120s | Performance avec beaucoup de résultats | `performance`, `slow` |
| `test_memory_usage_stability` | Variable | Stabilité de l'utilisation mémoire | `performance` |

## 🔧 Configuration Avancée

### Variables d'Environnement

| Variable | Obligatoire | Description | Exemple |
|----------|-------------|-------------|---------|
| `CONFLUENCE_URL` | ✅ | URL de l'instance Confluence | `https://company.atlassian.net` |
| `CONFLUENCE_PAT_TOKEN` | ✅ | Personal Access Token | `ATATTxxx...` |
| `CONFLUENCE_TEST_SPACE` | ❌ | Espace de test | `TEST` (défaut) |
| `CONFLUENCE_TIMEOUT` | ❌ | Timeout en secondes | `30` (défaut) |

### Système de Secrets - Priorité

Le système cherche les credentials dans cet ordre :

1. **Secrets par périmètre** : `{perimeter_code}-confluence-credentials`
2. **Secrets globaux** : `confluence-credentials`
3. **Format legacy** : `{perimeter_code}-confluence-pat-token`

### Création d'un Personal Access Token

1. Allez dans **Confluence** → **Settings** → **Personal Access Tokens**
2. Cliquez sur **Create token**
3. Donnez un nom au token (ex: "Tests d'intégration")
4. Sélectionnez les permissions : **Read** sur les espaces de test
5. Copiez le token généré

### Préparation de l'Espace de Test

Pour des tests optimaux, créez un espace de test avec :
- 3-5 pages avec du contenu varié
- 1-2 pages avec des pièces jointes (PDF, images)
- Une hiérarchie de pages (page parent → sous-pages)

### Configuration des Secrets

```bash
# Vérifier les secrets existants
python conf/etc/secrets/tests/setup_confluence_secrets.py --list

# Configurer interactivement
python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive

# Vérifier la configuration
python conf/etc/secrets/tests/setup_confluence_secrets.py --verify
```

## 🐛 Dépannage

### Erreur "Configuration Confluence non disponible"
- Vérifiez que le fichier `.env` existe et contient les bonnes variables
- Vérifiez que `CONFLUENCE_URL` et `CONFLUENCE_PAT_TOKEN` sont définis
- Vérifiez les secrets : `python conf/etc/secrets/tests/setup_confluence_secrets.py --verify`

### Erreur "Health check échoué"
- Vérifiez que l'URL Confluence est accessible
- Vérifiez que le PAT token est valide et non expiré
- Vérifiez les permissions du token
- Testez manuellement : `curl -H "Authorization: Bearer YOUR_TOKEN" YOUR_CONFLUENCE_URL/rest/api/content`

### Erreur "Aucun document trouvé"
- Vérifiez que l'espace de test existe
- Vérifiez que l'espace contient des pages
- Vérifiez les permissions de lecture sur l'espace
- Testez avec un autre espace : modifiez `CONFLUENCE_TEST_SPACE`

### Erreur "No credentials found in file system"
- Configurez les secrets : `python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive`
- Vérifiez les chemins : les secrets doivent être dans `conf/etc/secrets/tests/`

### Tests lents
- Utilisez `--run-fast` pour éviter les tests marqués `@pytest.mark.slow`
- Utilisez `--run-performance` au lieu de `--run-all-performance`
- Réduisez `max_results` dans la configuration de test

### Problèmes de mémoire
- Les tests de performance incluent un monitoring mémoire
- Utilisez `pytest -s` pour voir les détails en temps réel

## 📊 Interprétation des Résultats

### Succès ✅
```
✅ Health check réussi - Instance Confluence accessible
   URL: https://company.atlassian.net
   Espace de test: TEST
   Documents trouvés: 5
```

### Échec ❌
```
❌ Health check échoué: 401 Unauthorized
```

## 🔄 Intégration CI/CD

Pour intégrer ces tests dans votre pipeline CI/CD :

```yaml
# Exemple GitHub Actions
- name: Configuration des secrets de test
  run: |
    mkdir -p conf/etc/secrets/tests/confluence-credentials
    echo '{"pat_token": "${{ secrets.CONFLUENCE_PAT_TOKEN }}"}' > conf/etc/secrets/tests/confluence-credentials/secret

- name: Tests d'intégration Confluence
  env:
    CONFLUENCE_URL: ${{ secrets.CONFLUENCE_URL }}
    CONFLUENCE_TEST_SPACE: "CI_TEST"
  run: |
    python tests/loader/confluence/run_integration_tests.py --run-fast
```

## 📝 Bonnes Pratiques

1. **Utilisez un espace de test dédié** - Ne testez jamais sur des données de production
2. **Limitez les résultats** - Utilisez `max_results: 5-10` pour les tests
3. **Gérez les secrets** - Utilisez le système de fichiers pour les tests, GCP Secret Manager pour la production
4. **Tests progressifs** - Commencez par `--health-check`, puis `--run-fast`, puis `--run-performance`
5. **Nettoyage** - Les tests utilisent des répertoires temporaires qui se nettoient automatiquement
6. **Monitoring** - Les tests de performance fournissent des métriques détaillées
7. **Sécurité** - Les secrets de test sont dans `.gitignore` et ne sont jamais committés

## 🚀 Workflow Recommandé

1. **Première fois** :
   ```bash
   python conf/etc/secrets/tests/setup_confluence_secrets.py --interactive
   python tests/loader/confluence/run_integration_tests.py --check-config
   python tests/loader/confluence/run_integration_tests.py --health-check
   ```

2. **Développement quotidien** :
   ```bash
   python tests/loader/confluence/run_integration_tests.py --run-fast
   ```

3. **Avant release** :
   ```bash
   python tests/loader/confluence/run_integration_tests.py --run-all
   python tests/loader/confluence/run_integration_tests.py --run-performance
   ```

4. **Démonstration** :
   ```bash
   python tests/loader/confluence/demo_integration.py
   ```

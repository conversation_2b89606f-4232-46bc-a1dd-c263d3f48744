#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests unitaires adaptés pour l'authentification Confluence.

Adapté depuis src/kbotloadscheduler/loader/confluence/tests/test_auth.py
pour l'architecture kbot-load-scheduler.
"""

import unittest

import pytest

from kbotloadscheduler.loader.confluence.auth import AuthenticationManager
from kbotloadscheduler.loader.confluence.config import ConfluenceConfig
from kbotloadscheduler.loader.confluence.constants import AuthType


class TestAuthenticationManagerAdapted(unittest.TestCase):
    """Tests adaptés pour la classe AuthenticationManager."""

    def setUp(self):
        """Configuration des tests."""
        self.base_config_data = {
            "url": "https://test.atlassian.net",
            "default_space_key": "TEST"
        }

    @pytest.mark.unit
    @pytest.mark.confluence
    @pytest.mark.security
    def test_pat_token_authentication(self):
        """Test d'authentification avec PAT token."""
        config = ConfluenceConfig(
            **self.base_config_data,
            pat_token="test_pat_token"
        )

        auth_manager = AuthenticationManager(config)

        # L'auth_type est un attribut privé _auth_type
        assert auth_manager._auth_type == AuthType.PAT_TOKEN
        assert auth_manager.config == config

    @pytest.mark.unit
    @pytest.mark.confluence
    @pytest.mark.security
    def test_api_token_authentication(self):
        """Test d'authentification avec API token."""
        config = ConfluenceConfig(
            **self.base_config_data,
            username="<EMAIL>",
            api_token="test_api_token"
        )

        auth_manager = AuthenticationManager(config)

        # L'auth_type est un attribut privé _auth_type
        assert auth_manager._auth_type == AuthType.API_TOKEN
        assert auth_manager.config == config

    @pytest.mark.unit
    @pytest.mark.confluence
    @pytest.mark.security
    def test_get_auth_headers_and_auth_pat(self):
        """Test de génération des headers d'authentification PAT."""
        config = ConfluenceConfig(
            **self.base_config_data,
            pat_token="test_pat_token"
        )

        auth_manager = AuthenticationManager(config)
        headers, auth = auth_manager.get_auth_headers_and_auth()

        assert "Authorization" in headers
        assert headers["Authorization"].startswith("Bearer ")
        assert "test_pat_token" in headers["Authorization"]
        assert auth is None  # Pas de BasicAuth pour PAT

    @pytest.mark.unit
    @pytest.mark.confluence
    @pytest.mark.security
    def test_get_auth_headers_and_auth_api_token(self):
        """Test de génération des headers d'authentification API token."""
        config = ConfluenceConfig(
            **self.base_config_data,
            username="<EMAIL>",
            api_token="test_api_token"
        )

        auth_manager = AuthenticationManager(config)
        headers, auth = auth_manager.get_auth_headers_and_auth()

        assert "Accept" in headers
        assert auth is not None  # BasicAuth pour API token
        assert auth.login == "<EMAIL>"

    @pytest.mark.unit
    @pytest.mark.confluence
    @pytest.mark.security
    def test_auth_headers_security(self):
        """Test de sécurité des headers d'authentification."""
        config = ConfluenceConfig(
            **self.base_config_data,
            pat_token="secret_token"
        )

        auth_manager = AuthenticationManager(config)
        headers, _ = auth_manager.get_auth_headers_and_auth()

        # Vérifier que les headers contiennent l'authentification
        assert "Authorization" in headers

        # Vérifier que le token n'est pas exposé en clair dans les logs
        # (ceci serait testé avec un mock du système de logging)
        assert len(headers["Authorization"]) > 10  # Header non vide

    @pytest.mark.unit
    @pytest.mark.confluence
    @pytest.mark.security
    def test_multiple_auth_managers(self):
        """Test de création de multiples gestionnaires d'authentification."""
        config1 = ConfluenceConfig(
            **self.base_config_data,
            pat_token="token1"
        )

        config2 = ConfluenceConfig(
            **self.base_config_data,
            username="<EMAIL>",
            api_token="token2"
        )

        auth_manager1 = AuthenticationManager(config1)
        auth_manager2 = AuthenticationManager(config2)

        assert auth_manager1._auth_type == AuthType.PAT_TOKEN
        assert auth_manager2._auth_type == AuthType.API_TOKEN
        assert auth_manager1.config != auth_manager2.config


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test de bout en bout pour le module Confluence dans kbot-load-scheduler.
Ce test vérifie l'intégration complète du module Confluence avec l'architecture du projet.
"""

import os
import sys
import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock
import datetime
from typing import Dict, Any, List

# Ajouter le chemin du projet au PYTHONPATH
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from kbotloadscheduler.bean.beans import SourceBean, DocumentBean
from kbotloadscheduler.dependency.container import Container
from kbotloadscheduler.loader.loader_manager import LoaderManager
from kbotloadscheduler.loader.confluence.confluence_loader import ConfluenceLoader
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret

# Import des mocks
from tests.testutils.mock_confluence import MockConfluenceAPI
from tests.testutils.mock_gcs import mock_gcs_bucket, mock_gcs_blob, MockGCSClient


# Configuration pour les tests
TEST_CONFLUENCE_URL = "https://test-confluence.example.com"
TEST_PERIMETER_CODE = "test-perimeter"
TEST_PAT_TOKEN = "test-pat-token-12345"


@pytest.fixture
def mock_config_with_secret():
    """Fixture pour créer un ConfigWithSecret mocké."""
    from dependency_injector import providers

    # Créer une configuration de test
    config = providers.Configuration()
    config.env.from_value('tests')  # Important : définir env='tests' pour éviter l'initialisation du client Secret Manager
    config.gcp_project_id.from_value('')

    # Créer l'instance ConfigWithSecret
    config_with_secret = ConfigWithSecret(config=config)

    # Mocker toutes les méthodes get_* pour éviter les appels réels à Secret Manager
    config_with_secret.get_confluence_credentials = MagicMock(return_value={
        "pat_token": TEST_PAT_TOKEN
    })
    config_with_secret.get_basic_client_id = MagicMock(return_value="fake-basic-client-id")
    config_with_secret.get_basic_client_secret = MagicMock(return_value="fake-basic-client-secret")
    config_with_secret.get_kbot_loadscheduler_client_id = MagicMock(return_value="fake-kbot-client-id")
    config_with_secret.get_kbot_loadscheduler_client_secret = MagicMock(return_value="fake-kbot-client-secret")
    config_with_secret.get_sharepoint_client_config = MagicMock(return_value={"client_id": "fake-sharepoint-id"})
    config_with_secret.get_sharepoint_client_private_key = MagicMock(return_value="fake-sharepoint-key")

    return config_with_secret


@pytest.fixture
def mock_env_vars():
    """Fixture pour configurer les variables d'environnement requises."""
    original_env = os.environ.copy()

    os.environ["CONFLUENCE_URL"] = TEST_CONFLUENCE_URL
    os.environ["DEFAULT_SPACE_KEY"] = "TEST"

    yield

    # Restaurer l'environnement original
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def mock_confluence_client():
    """Fixture pour simuler le client Confluence."""
    # Créer une instance du mock Confluence API
    mock_api = MockConfluenceAPI()

    # Mocker la méthode search_content du ConfluenceClient
    with patch("kbotloadscheduler.loader.confluence.client.ConfluenceClient.search_content") as mock_search:
        async def mock_search_content(search_criteria):
            # Convertir nos pages mockées en objets ContentItem mockés
            content_items = []
            for page_data in mock_api.get_mock_pages():
                content_item = MagicMock()
                content_item.id = page_data["id"]
                content_item.title = page_data["title"]
                content_item.type = page_data["type"]
                content_item.web_ui_link = page_data["_links"]["webui"]
                content_item.last_updated = datetime.datetime.now().isoformat()

                # Ajouter les attachments mockés
                content_item.attachments = []
                for att_data in mock_api.get_mock_attachments(page_data["id"]):
                    attachment = MagicMock()
                    attachment.id = att_data["id"]
                    attachment.title = att_data["title"]
                    attachment.download_link = att_data["_links"]["download"]
                    attachment.created = datetime.datetime.now().isoformat()
                    content_item.attachments.append(attachment)

                content_items.append(content_item)

            return content_items

        mock_search.side_effect = mock_search_content

        # Mocker aussi l'orchestrateur pour le test get_document
        with patch("kbotloadscheduler.loader.confluence.orchestrator.SyncOrchestrator.run") as mock_run:
            async def mock_orchestrator_run():
                # Retourner des statistiques de synchronisation mockées
                return {
                    "total_content_items": len(mock_api.get_mock_pages()),
                    "stored_content_items": len(mock_api.get_mock_pages()),
                    "processing_time_seconds": 0.5,
                    "start_time": datetime.datetime.now(),
                    "end_time": datetime.datetime.now(),
                    "errors": 0,
                    "warnings": 0
                }

            mock_run.side_effect = mock_orchestrator_run

            yield mock_api


@pytest.fixture
def mock_gcs():
    """Fixture pour simuler Google Cloud Storage."""
    with patch("google.cloud.storage.Client") as mock_client:
        # Configurer les mocks GCS
        mock_client.return_value = MockGCSClient()
        yield mock_client


@pytest.fixture
def mock_basic_client():
    """Fixture pour mocker le ProcedureSheetClient du BasicLoader."""
    with patch("kbotloadscheduler.loader.basic.basic_loader.ProcedureSheetClient") as mock_client:
        # Créer un mock simple qui ne fait pas d'appels HTTP
        mock_instance = MagicMock()
        mock_client.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def container(mock_config_with_secret, mock_env_vars, mock_basic_client):
    """Fixture pour créer le container de dépendances avec ConfigWithSecret mocké."""
    container = Container()

    # Remplacer le provider configWithSecret par notre mock
    container.configWithSecret.override(mock_config_with_secret)

    yield container


@pytest.fixture
def test_source():
    """Fixture pour créer une source de test."""
    source_config = {
        "spaces": ["TEST", "DOCS"],
        "max_results": 10,
        "include_attachments": True,
        "content_types": ["page"],
        "labels": ["test-label"]
    }

    source = SourceBean(
        id=1,
        code="test_confluence",
        label="Test Confluence",
        src_type="confluence",
        configuration=json.dumps(source_config),
        last_load_time=**********,  # Timestamp de test
        load_interval=24,  # 24 heures
        domain_code="test-domain",
        perimeter_code=TEST_PERIMETER_CODE
    )

    yield source


def setup_mock_confluence_data(mock_api: MockConfluenceAPI):
    """Configure les données simulées pour l'API Confluence."""
    # Créer quelques pages Confluence simulées
    pages = [
        {
            "id": f"page{i}",
            "title": f"Test Page {i}",
            "type": "page",
            "space": {"key": "TEST", "name": "Test Space"},
            "version": {"number": 1, "when": datetime.datetime.now().isoformat()},
            "body": {"storage": {"value": f"<p>Test content {i}</p>"}},
            "_links": {"webui": f"/pages/viewpage.action?pageId=page{i}"}
        }
        for i in range(1, 4)
    ]
    
    # Ajouter des pièces jointes à la première page
    attachments = [
        {
            "id": f"att{i}",
            "title": f"attachment{i}.pdf",
            "extensions": {"fileSize": 1024, "mediaType": "application/pdf"},
            "version": {"createdDate": datetime.datetime.now().isoformat()},
            "_links": {"download": f"/download/attachments/page1/attachment{i}.pdf"}
        }
        for i in range(1, 3)
    ]
    
    # Configurer le mock pour renvoyer ces données
    mock_api.set_mock_pages(pages)
    mock_api.set_mock_attachments("page1", attachments)
    mock_api.set_mock_attachment_content(b"test attachment content")


class TestConfluenceEndToEnd:
    """Tests de bout en bout pour le module Confluence."""
    
    def test_get_document_list(self, container, test_source, mock_confluence_client, mock_env_vars):
        """Teste la récupération de la liste des documents Confluence."""
        # Configurer les données simulées
        setup_mock_confluence_data(mock_confluence_client)
        
        # Obtenir le loader manager depuis le container
        loader_manager = container.loader_manager()
        
        # Récupérer le loader Confluence
        confluence_loader = loader_manager.get_loader("confluence")
        assert confluence_loader is not None, "Le loader Confluence n'a pas été trouvé"
        
        # Obtenir la liste des documents
        documents = confluence_loader.get_document_list(test_source)
        
        # Vérifications
        assert len(documents) > 0, "Aucun document n'a été récupéré"
        
        # Vérifier le format des documents
        for doc in documents:
            assert isinstance(doc, DocumentBean), "Le document n'est pas une instance de DocumentBean"
            assert doc.id.startswith(f"{test_source.domain_code}/{test_source.code}/"), \
                "L'ID du document ne suit pas le format attendu"
            assert doc.name is not None and doc.name != "", "Le nom du document est vide"
            assert doc.path is not None and doc.path != "", "Le chemin du document est vide"
            assert doc.modification_time is not None, "La date de modification est manquante"
    
    def test_get_document(self, container, test_source, mock_confluence_client, mock_gcs, mock_env_vars):
        """Teste la récupération d'un document spécifique."""
        # Configurer les données simulées
        setup_mock_confluence_data(mock_confluence_client)
        
        # Créer un DocumentBean pour le test
        document = DocumentBean(
            id=f"{test_source.domain_code}/{test_source.code}/page_page1",
            name="Test Page 1",
            path="/pages/viewpage.action?pageId=page1",
            modification_time=datetime.datetime.now().isoformat()
        )
        
        # Chemin de sortie GCS
        output_path = "test-bucket/confluence-test/page1"
        
        # Obtenir le loader manager depuis le container
        loader_manager = container.loader_manager()
        
        # Récupérer le loader Confluence
        confluence_loader = loader_manager.get_loader("confluence")
        
        # Patcher la méthode run() de SyncOrchestrator pour simuler une synchronisation réussie
        with patch("kbotloadscheduler.loader.confluence.orchestrator.SyncOrchestrator.run") as mock_run:
            mock_run.return_value = {
                "total_content_items": 1,
                "stored_content_items": 1,
                "processing_time_seconds": 0.5
            }
            
            # Récupérer le document
            metadata = confluence_loader.get_document(test_source, document, output_path)
            
            # Vérifications
            assert metadata is not None, "Aucune métadonnée n'a été retournée"
            assert "document_id" in metadata, "document_id manquant dans les métadonnées"
            assert metadata["document_id"] == document.id, "L'ID du document ne correspond pas"
            assert "location" in metadata, "location manquant dans les métadonnées"
            assert metadata["location"] == output_path, "Le chemin de sortie ne correspond pas"
            assert "source_type" in metadata and metadata["source_type"] == "confluence", \
                "Le type de source ne correspond pas"
            assert "confluence_sync_stats" in metadata, "Statistiques de synchronisation manquantes"
    
    def test_loader_manager_integration(self, container, mock_env_vars):
        """Teste que le LoaderManager est correctement configuré avec le loader Confluence."""
        # Obtenir le loader manager depuis le container
        loader_manager = container.loader_manager()

        # Vérifier que le LoaderManager est bien une instance de LoaderManager
        assert isinstance(loader_manager, LoaderManager), \
            "Le container ne retourne pas une instance de LoaderManager"

        # Récupérer le loader Confluence
        confluence_loader = loader_manager.get_loader("confluence")
        assert confluence_loader is not None, "Le loader Confluence n'est pas disponible"
        assert isinstance(confluence_loader, ConfluenceLoader), \
            "Le loader renvoyé n'est pas une instance de ConfluenceLoader"

        # Vérifier que le loader a le bon type
        assert confluence_loader._loader_type == "confluence", \
            "Le loader n'a pas le bon type"


if __name__ == "__main__":
    pytest.main(["-xvs", __file__])

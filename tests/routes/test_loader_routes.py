import json
from kbotloadscheduler.bean.beans import Metadata
from tests.testutils.mock_gcs import MockGcs
from data.gcs_repo_test_data import GcsRepoTestData
from data.basic_connect_mock import basic_connect_mock


class TestLoaderRoutes:

    def test_get_document_list(self, mocker, requests_mock, client):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        GcsRepoTestData.write_get_list(my_mock_gcs)
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)

        # Here we won't use BasicLoader but has we create it, we must mock connections calls
        basic_connect_mock(mocker, requests_mock)

        get_list_file = 'gs://' + GcsRepoTestData.WORK_BUCKET + '/' + GcsRepoTestData.GET_LIST_FILE
        response = client.post('/loader/list/'+GcsRepoTestData.PERIMETER_CODE,
                               json={'get_list_file': get_list_file})
        assert response.status_code == 200
        actual_document_list = response.json()

        expected_document_list = GcsRepoTestData.get_serialized_document_list_for_source()
        assert actual_document_list == expected_document_list

        expected_file_list_content = {
            'source': GcsRepoTestData.SOURCE,
            'documents': GcsRepoTestData.get_serialized_document_list_for_source()
        }
        blob_list = my_mock_gcs.return_blob(GcsRepoTestData.WORK_BUCKET, GcsRepoTestData.LIST_FILE)
        actual_file_list_content = json.loads(blob_list.download_as_string())
        assert actual_file_list_content == expected_file_list_content

    def test_get_document(self, mocker, requests_mock, client):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{GcsRepoTestData.WORK_BUCKET}')
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)
        document = GcsRepoTestData.get_serialized_document_list_for_source()[0]
        document_get_file = GcsRepoTestData.write_document_get_file(my_mock_gcs, document)
        blob = my_mock_gcs.return_blob(
            GcsRepoTestData.WORK_BUCKET,
            document_get_file,
            get_blob_call=True
        )

        # Here we won't use BasicLoader but has we create it, we must mock connections calls
        basic_connect_mock(mocker, requests_mock)

        response = client.post('/loader/document/'+GcsRepoTestData.PERIMETER_CODE,
                               json={
                                   'document_get_file': 'gs://' + GcsRepoTestData.WORK_BUCKET + '/' + document_get_file
                               })
        assert response.status_code == 200
        actual_doc_with_md = response.json()

        doc_id = document.get('id')
        doc_name = document.get('name')
        metadata = {
            Metadata.DOCUMENT_ID: doc_id,
            Metadata.DOCUMENT_NAME: doc_name,
            Metadata.DOMAIN_CODE: GcsRepoTestData.DOMAINE_CODE,
            Metadata.SOURCE_CODE: GcsRepoTestData.SOURCE_CODE,
            Metadata.SOURCE_TYPE: 'gcs',
            Metadata.SOURCE_CONF: json.dumps(GcsRepoTestData.CONF_SOURCE),
            Metadata.SOURCE_URL: f'gs://{GcsRepoTestData.REPO_BUCKET}/{doc_name}',
            Metadata.LOCATION: f'gs://{GcsRepoTestData.WORK_BUCKET}/'
                               + f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc_name}',
            Metadata.CREATION_TIME: GcsRepoTestData.DATE_DICT.get(doc_name).get('creation_str_time'),
            Metadata.MODIFICATION_TIME: GcsRepoTestData.DATE_DICT.get(doc_name).get('modification_str_time')
        }
        expected_doc_with_md = {'document': document, 'metadata': metadata}

        assert actual_doc_with_md == expected_doc_with_md

        doc_name = expected_doc_with_md.get('document').get('name')
        blob = my_mock_gcs.return_blob(
            GcsRepoTestData.WORK_BUCKET,
            f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc_name}',
            get_blob_call=True
        )
        assert blob is not None

        metadata_path = f'{GcsRepoTestData.RELATIVE_OUTPUT}/docs/{doc_name}.metadata.json'
        blob_metadata = my_mock_gcs.return_blob(GcsRepoTestData.WORK_BUCKET, metadata_path)
        actual_metadata_content = json.loads(blob_metadata.download_as_string())
        assert actual_metadata_content == expected_doc_with_md.get('metadata')

    def test_get_document_error(self, mocker, requests_mock, client):
        my_mock_gcs = MockGcs(mocker, "kbotloadscheduler.gcs.gcs_utils.storage")
        my_mock_gcs.add_bucket(f'gs://{GcsRepoTestData.WORK_BUCKET}')
        GcsRepoTestData.prepare_gcs_repo(my_mock_gcs)
        document = GcsRepoTestData.get_serialized_document_list_for_source()[0]
        document['name'] = "fake"
        document_get_file = GcsRepoTestData.write_document_get_file(my_mock_gcs, document)

        # Here we won't use BasicLoader but has we create it, we must mock connections calls
        basic_connect_mock(mocker, requests_mock)

        response = client.post('/loader/document/'+GcsRepoTestData.PERIMETER_CODE,
                               json={
                                   'document_get_file': 'gs://' + GcsRepoTestData.WORK_BUCKET + '/' + document_get_file
                               })
        assert response.status_code == 400
# Guide de Migration vers une Architecture Synchrone

## 🎯 Objectif

Migrer le ConfluenceLoader d'une architecture mixte asyncio/synchrone vers une architecture entièrement synchrone pour améliorer la cohérence, la maintenabilité et les performances.

## 📋 Plan de Migration

### Phase 1: Préparation et Analyse

#### 1.1 Audit du code existant
- [ ] Identifier tous les composants asynchrones
- [ ] Analyser les dépendances aiohttp
- [ ] Documenter les patterns asyncio utilisés
- [ ] Mesurer les performances actuelles

#### 1.2 Définition des interfaces
- [ ] Maintenir l'interface AbstractLoader
- [ ] Définir les nouvelles signatures synchrones
- [ ] Planifier la compatibilité ascendante

### Phase 2: Implémentation du Client Synchrone

#### 2.1 Nouveau ConfluenceClient synchrone

```python
# src/kbotloadscheduler/loader/confluence/sync_client.py
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
import logging
import time

class SyncConfluenceClient:
    """Client Confluence synchrone utilisant requests + ThreadPoolExecutor."""
    
    def __init__(self, config: ConfluenceConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Session réutilisable
        self.session = requests.Session()
        self._setup_session()
        
        # Pool de threads pour la parallélisation
        self.executor = ThreadPoolExecutor(
            max_workers=getattr(config, 'max_concurrent_calls', 5)
        )
    
    def _setup_session(self):
        """Configure la session requests."""
        # Headers d'authentification
        auth_headers = self._get_auth_headers()
        self.session.headers.update(auth_headers)
        
        # Configuration des timeouts
        self.session.timeout = self.config.timeout or 30
        
        # Configuration des retries
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=self.config.retry_config.max_retries,
            backoff_factor=self.config.retry_config.initial_backoff,
            status_forcelist=[429, 500, 502, 503, 504]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Génère les headers d'authentification."""
        if self.config.username and self.config.api_token:
            import base64
            credentials = f"{self.config.username}:{self.config.api_token}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            return {
                'Authorization': f'Basic {encoded_credentials}',
                'Content-Type': 'application/json'
            }
        return {}
    
    def search_content(self, criteria: SearchCriteria) -> List[ContentItem]:
        """Recherche synchrone avec parallélisation."""
        cql = criteria.to_cql()
        
        # Estimation du nombre total de résultats
        total_results = self._estimate_total_results(cql)
        
        if total_results == 0:
            return []
        
        # Calcul des pages à récupérer
        page_size = min(100, criteria.max_results or 100)
        pages = self._calculate_pages(total_results, criteria.max_results, page_size)
        
        # Récupération parallèle des pages
        content_items = self._fetch_pages_parallel(cql, pages)
        
        # Limitation des résultats
        if criteria.max_results and len(content_items) > criteria.max_results:
            content_items = content_items[:criteria.max_results]
        
        return content_items
    
    def _estimate_total_results(self, cql: str) -> int:
        """Estime le nombre total de résultats."""
        try:
            response = self._make_cql_request(cql, start=0, limit=1)
            return response.get('totalSize', 0)
        except Exception as e:
            self.logger.warning(f"Impossible d'estimer les résultats: {e}")
            return 0
    
    def _calculate_pages(self, total: int, max_results: Optional[int], page_size: int) -> List[tuple]:
        """Calcule les pages à récupérer."""
        effective_total = min(total, max_results) if max_results else total
        pages = []
        
        for start in range(0, effective_total, page_size):
            limit = min(page_size, effective_total - start)
            pages.append((start, limit))
        
        return pages
    
    def _fetch_pages_parallel(self, cql: str, pages: List[tuple]) -> List[ContentItem]:
        """Récupère les pages en parallèle."""
        content_items = []
        
        # Soumettre toutes les tâches
        future_to_page = {
            self.executor.submit(self._fetch_single_page, cql, start, limit): (start, limit)
            for start, limit in pages
        }
        
        # Récupérer les résultats au fur et à mesure
        for future in as_completed(future_to_page):
            start, limit = future_to_page[future]
            try:
                page_items = future.result()
                content_items.extend(page_items)
            except Exception as e:
                self.logger.error(f"Erreur page {start}-{start+limit}: {e}")
        
        return content_items
    
    def _fetch_single_page(self, cql: str, start: int, limit: int) -> List[ContentItem]:
        """Récupère une seule page de résultats."""
        response_data = self._make_cql_request(cql, start, limit)
        results = response_data.get('results', [])
        
        content_items = []
        for result_json in results:
            try:
                content_item = ContentItem.from_json(result_json)
                content_items.append(content_item)
            except Exception as e:
                self.logger.warning(f"Erreur conversion ContentItem: {e}")
        
        return content_items
    
    def _make_cql_request(self, cql: str, start: int = 0, limit: int = 100) -> Dict[str, Any]:
        """Effectue une requête CQL synchrone."""
        url = f"{self.config.url.rstrip('/')}/rest/api/content/search"
        params = {
            'cql': cql,
            'start': start,
            'limit': limit,
            'expand': 'version,space,body.storage,metadata.labels,children.attachment'
        }
        
        response = self.session.get(url, params=params)
        response.raise_for_status()
        
        return response.json()
    
    def close(self):
        """Ferme les ressources."""
        self.executor.shutdown(wait=True)
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
```

#### 2.2 Adaptation de l'orchestrateur

```python
# src/kbotloadscheduler/loader/confluence/sync_orchestrator.py
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any

class SyncOrchestrator:
    """Orchestrateur synchrone pour Confluence."""
    
    def __init__(self, config: ConfluenceConfig, criteria: SearchCriteria, 
                 storage_config: StorageConfig, processing_config: ProcessingConfig):
        self.config = config
        self.criteria = criteria
        self.storage_config = storage_config
        self.processing_config = processing_config
        
        self.client = SyncConfluenceClient(config)
        self.executor = ThreadPoolExecutor(
            max_workers=processing_config.max_parallel_downloads
        )
    
    def run(self) -> Dict[str, Any]:
        """Exécute la synchronisation complète."""
        try:
            # Rechercher les contenus
            content_items = self.client.search_content(self.criteria)
            
            # Traiter les contenus en parallèle
            results = self._process_content_parallel(content_items)
            
            return {
                'total_items': len(content_items),
                'processed_items': len(results),
                'success': True
            }
        finally:
            self.client.close()
            self.executor.shutdown(wait=True)
    
    def _process_content_parallel(self, content_items: List[ContentItem]) -> List[Dict[str, Any]]:
        """Traite les contenus en parallèle."""
        future_to_item = {
            self.executor.submit(self._process_single_item, item): item
            for item in content_items
        }
        
        results = []
        for future in as_completed(future_to_item):
            item = future_to_item[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                self.logger.error(f"Erreur traitement {item.id}: {e}")
        
        return results
    
    def _process_single_item(self, item: ContentItem) -> Dict[str, Any]:
        """Traite un seul élément de contenu."""
        # Logique de traitement synchrone
        return {'item_id': item.id, 'status': 'processed'}
```

### Phase 3: Migration du ConfluenceLoader

#### 3.1 Nouveau ConfluenceLoader synchrone

```python
# Mise à jour de src/kbotloadscheduler/loader/confluence/confluence_loader.py
class ConfluenceLoader(AbstractLoader):
    """Loader Confluence entièrement synchrone."""
    
    def get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        """Récupère la liste des documents de manière synchrone."""
        try:
            self.logger.info(f"Getting document list for Confluence source: {source.code}")
            
            # Créer les configurations
            confluence_config = self._create_confluence_config(source)
            search_criteria = self._create_search_criteria(source)
            
            # Utiliser le nouveau client synchrone
            with SyncConfluenceClient(confluence_config) as client:
                content_items = client.search_content(search_criteria)
            
            # Convertir en DocumentBean
            documents = []
            for item in content_items:
                doc_bean = self._content_item_to_document_bean(source, item)
                documents.append(doc_bean)
                
                # Ajouter les attachments
                for attachment in item.attachments:
                    att_bean = self._attachment_to_document_bean(source, item, attachment)
                    documents.append(att_bean)
            
            self.logger.info(f"Found {len(documents)} documents for source {source.code}")
            return documents
            
        except Exception as e:
            self.logger.error(f"Error getting document list: {str(e)}")
            raise LoaderException(f"Failed to get document list: {str(e)}")
```

### Phase 4: Tests et Validation

#### 4.1 Tests de performance
- [ ] Comparer les performances avant/après
- [ ] Tester la charge avec plusieurs appels simultanés
- [ ] Valider l'utilisation mémoire

#### 4.2 Tests fonctionnels
- [ ] Vérifier la compatibilité des résultats
- [ ] Tester tous les cas d'usage existants
- [ ] Valider la gestion d'erreurs

### Phase 5: Déploiement

#### 5.1 Déploiement progressif
- [ ] Feature flag pour basculer entre ancien/nouveau
- [ ] Tests en environnement de staging
- [ ] Monitoring des performances

#### 5.2 Nettoyage
- [ ] Supprimer l'ancien code asyncio
- [ ] Mettre à jour la documentation
- [ ] Former l'équipe sur la nouvelle architecture

## 🔧 Outils et Scripts

### Script de migration automatique
```bash
# scripts/migrate_to_sync.sh
#!/bin/bash

echo "🚀 Migration vers architecture synchrone"

# Backup du code existant
cp -r src/kbotloadscheduler/loader/confluence src/kbotloadscheduler/loader/confluence.backup

# Installer les nouvelles dépendances
pip install requests urllib3

# Exécuter les tests de migration
python -m pytest tests/migration/ -v

echo "✅ Migration terminée"
```

## 📊 Métriques de Succès

- **Performance** : Temps de réponse ≤ temps actuel
- **Stabilité** : 0 erreur de concurrence
- **Maintenabilité** : Réduction de 50% de la complexité cyclomatique
- **Tests** : Couverture ≥ 90%

## 🚨 Risques et Mitigation

| Risque | Impact | Probabilité | Mitigation |
|--------|--------|-------------|------------|
| Dégradation performance | Élevé | Moyen | Tests de charge, optimisation ThreadPool |
| Régression fonctionnelle | Élevé | Faible | Tests complets, déploiement progressif |
| Résistance équipe | Moyen | Moyen | Formation, documentation claire |

## 📚 Ressources

- [Documentation ThreadPoolExecutor](https://docs.python.org/3/library/concurrent.futures.html)
- [Requests Performance](https://requests.readthedocs.io/en/latest/user/advanced/#performance)
- [Guide des bonnes pratiques](./BEST_PRACTICES.md)

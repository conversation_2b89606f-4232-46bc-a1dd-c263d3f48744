# Analyse de l'architecture mixte asyncio/synchrone

## Problématique identifiée

Le module Confluence utilise beaucoup asyncio et aiohttp. Le reste de kbot-load-scheduler (et les autres loaders) semblent majoritairement synchrones (utilisant requests).
Le ConfluenceLoader exécute des boucles asyncio de manière synchrone (loop.run_until_complete). Bien que cela fonctionne pour l'intégration, cela peut être une source de complexité et potentiellement de blocage si mal géré à grande échelle.

## Analyse détaillée de l'architecture

### État actuel

#### 🏗️ Architecture générale de kbot-load-scheduler
- **Application FastAPI** : Routes asynchrones mais logique métier synchrone
- **Loaders existants** : Tous synchrones (BasicLoader, SharepointLoader, GcsLoader)
- **Services et managers** : Architecture synchrone avec injection de dépendances
- **Interface AbstractLoader** : Méthodes synchrones

#### ⚡ ConfluenceLoader spécifique
- **Client Confluence** : Entièrement asynchrone (aiohttp, asyncio)
- **Orchestrateur** : Asynchrone avec gestion de pools de threads
- **Intégration** : Utilise `loop.run_until_complete()` pour "synchroniser" le code asyncio

### 🚨 Problèmes identifiés

#### 1. **Blocage potentiel du thread principal**
```python
# Dans confluence_loader.py lignes 132-137
loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)
try:
    content_items = loop.run_until_complete(orchestrator.client.search_content(search_criteria))
finally:
    loop.close()
```
- Crée une nouvelle boucle d'événements à chaque appel
- Bloque le thread principal pendant l'exécution asyncio
- Peut impacter les performances de l'API FastAPI

#### 2. **Complexité de maintenance**
- Deux paradigmes de programmation dans le même projet
- Gestion d'erreurs différente entre sync/async
- Tests plus complexes (mocking asyncio vs requests)

#### 3. **Incohérence architecturale**
- Interface AbstractLoader synchrone vs implémentation Confluence asynchrone
- Autres loaders utilisent `requests` (synchrone)
- Mélange de patterns dans le même système

### 📊 Impact sur les performances

#### Scénarios problématiques
1. **Charge élevée** : Multiples appels simultanés au ConfluenceLoader
2. **Gros volumes** : Synchronisation de milliers de documents
3. **Timeouts** : Blocage prolongé du thread principal

#### Métriques à surveiller
- Temps de réponse des API routes
- Utilisation CPU/mémoire
- Nombre de threads actifs
- Latence des appels Confluence

## Solutions proposées

### 🎯 Option 1 : Uniformisation vers le synchrone (Recommandée)

#### Avantages
- ✅ Cohérence avec l'architecture existante
- ✅ Simplicité de maintenance
- ✅ Compatibilité avec l'interface AbstractLoader
- ✅ Tests plus simples

#### Implémentation
```python
# Remplacer aiohttp par requests + ThreadPoolExecutor
import requests
from concurrent.futures import ThreadPoolExecutor

class ConfluenceClient:
    def __init__(self, config):
        self.session = requests.Session()
        self.executor = ThreadPoolExecutor(max_workers=config.max_concurrent_calls)
    
    def search_content(self, criteria):
        # Logique synchrone avec parallélisation via ThreadPoolExecutor
        pass
```

#### Migration requise
- Réécrire le client Confluence en synchrone
- Adapter l'orchestrateur pour utiliser ThreadPoolExecutor
- Maintenir les optimisations de performance existantes

### 🚀 Option 2 : Uniformisation vers l'asynchrone

#### Avantages
- ✅ Meilleures performances I/O
- ✅ Scalabilité supérieure
- ✅ Cohérence avec FastAPI moderne

#### Inconvénients
- ❌ Refactoring majeur de tous les loaders
- ❌ Changement de l'interface AbstractLoader
- ❌ Impact sur tous les tests existants

### 🔧 Option 3 : Amélioration de l'approche actuelle

#### Optimisations possibles
```python
# Utiliser asyncio.run() au lieu de loop.run_until_complete()
import asyncio

class ConfluenceLoader:
    def get_document_list(self, source):
        return asyncio.run(self._async_get_document_list(source))
    
    async def _async_get_document_list(self, source):
        # Logique asynchrone existante
        pass
```

#### Avantages
- ✅ Amélioration immédiate sans refactoring majeur
- ✅ Meilleure gestion des boucles d'événements
- ✅ Compatibilité maintenue

## Recommandation finale

**Option 1 (Uniformisation synchrone)** est recommandée car :

1. **Cohérence architecturale** avec le reste du projet
2. **Simplicité de maintenance** à long terme
3. **Compatibilité** avec l'interface existante
4. **Performance acceptable** avec ThreadPoolExecutor pour la parallélisation

### Plan de migration
1. **Phase 1** : Créer un client Confluence synchrone basé sur requests
2. **Phase 2** : Adapter l'orchestrateur pour utiliser ThreadPoolExecutor
3. **Phase 3** : Migrer les tests et la documentation
4. **Phase 4** : Déprécier progressivement l'ancien code asyncio

## Implémentation immédiate (Option 3 améliorée)

### Amélioration du ConfluenceLoader actuel

```python
# src/kbotloadscheduler/loader/confluence/confluence_loader.py
import asyncio
import logging
from typing import List, Dict, Any

class ConfluenceLoader(AbstractLoader):
    def __init__(self, config_with_secret: ConfigWithSecret):
        super().__init__("confluence")
        # ... initialisation existante ...
        self._loop_policy = asyncio.DefaultEventLoopPolicy()

    def get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        """Version améliorée avec gestion optimisée d'asyncio"""
        try:
            self.logger.info(f"Getting document list for Confluence source: {source.code}")

            # Utiliser asyncio.run() pour une meilleure gestion
            return asyncio.run(self._async_get_document_list(source))

        except Exception as e:
            self.logger.error(f"Error getting document list for source {source.code}: {str(e)}")
            raise LoaderException(f"Failed to get document list: {str(e)}")

    async def _async_get_document_list(self, source: SourceBean) -> List[DocumentBean]:
        """Logique asynchrone extraite"""
        # Créer les configurations
        confluence_config = self._create_confluence_config(source)
        search_criteria = self._create_search_criteria(source)

        # Configuration de stockage temporaire
        storage_config = StorageConfig(
            storage_type="filesystem",
            output_dir="/tmp/confluence_temp"
        )

        processing_config = ProcessingConfig.from_env()

        # Créer l'orchestrateur
        orchestrator = SyncOrchestrator(
            confluence_config,
            search_criteria,
            storage_config,
            processing_config
        )

        # Récupérer seulement la liste des contenus
        content_items = await orchestrator.client.search_content(search_criteria)

        # Convertir en DocumentBean
        documents = []
        for item in content_items:
            doc_bean = self._content_item_to_document_bean(source, item)
            documents.append(doc_bean)

            # Ajouter les attachments comme documents séparés
            for attachment in item.attachments:
                att_bean = self._attachment_to_document_bean(source, item, attachment)
                documents.append(att_bean)

        self.logger.info(f"Found {len(documents)} documents for source {source.code}")
        return documents

    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]:
        """Version améliorée avec gestion optimisée d'asyncio"""
        try:
            self.logger.info(f"Getting document: {document.id}")

            # Utiliser asyncio.run() pour une meilleure gestion
            return asyncio.run(self._async_get_document(source, document, output_path))

        except Exception as e:
            self.logger.error(f"Error getting document {document.id}: {str(e)}")
            raise LoaderException(f"Failed to get document: {str(e)}")

    async def _async_get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> Dict[str, Any]:
        """Logique asynchrone extraite"""
        # Créer les configurations
        confluence_config = self._create_confluence_config(source)
        search_criteria = self._create_search_criteria(source)

        # Configuration de stockage vers GCS
        storage_config = StorageConfig(
            storage_type="gcs",
            gcs_bucket_name=self._extract_bucket_from_path(output_path),
            gcs_base_prefix=self._extract_prefix_from_path(output_path)
        )

        processing_config = ProcessingConfig.from_env()

        # Créer l'orchestrateur
        orchestrator = SyncOrchestrator(
            confluence_config,
            search_criteria,
            storage_config,
            processing_config
        )

        # Exécuter la synchronisation complète
        sync_result = await orchestrator.run()

        # Construire les métadonnées
        metadata = {
            Metadata.DOCUMENT_ID: document.id,
            Metadata.DOCUMENT_NAME: document.name,
            Metadata.LOCATION: output_path,
            Metadata.DOMAIN_CODE: source.domain_code,
            Metadata.SOURCE_CODE: source.code,
            Metadata.SOURCE_TYPE: "confluence",
            Metadata.MODIFICATION_TIME: document.modification_time
        }

        # Ajouter des métadonnées spécifiques à Confluence
        if sync_result:
            metadata["confluence_sync_stats"] = sync_result

        return metadata
```

### Avantages de cette amélioration

1. **Meilleure gestion des boucles d'événements** : `asyncio.run()` vs `loop.run_until_complete()`
2. **Code plus lisible** : Séparation claire sync/async
3. **Gestion d'erreurs améliorée** : Plus de robustesse
4. **Compatibilité maintenue** : Interface AbstractLoader inchangée

## Conclusion

L'approche actuelle fonctionne mais présente des risques de performance et de maintenance. Une uniformisation vers le synchrone permettrait une meilleure cohérence architecturale tout en maintenant les performances grâce à la parallélisation via ThreadPoolExecutor.

**Actions recommandées :**

1. **Court terme** : Implémenter l'amélioration Option 3 pour une meilleure robustesse
2. **Moyen terme** : Planifier la migration vers une architecture entièrement synchrone
3. **Long terme** : Évaluer l'opportunité de migrer vers une architecture entièrement asynchrone

Cette approche progressive permet d'améliorer immédiatement la situation tout en préparant une migration plus importante si nécessaire.
